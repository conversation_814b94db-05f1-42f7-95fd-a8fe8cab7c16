C51 COMPILER V9.54   KEY                                                                   07/07/2025 20:22:00 PAGE 1   


C51 COMPILER V9.54, COMPILATION OF MODULE KEY
OBJECT MODULE PLACED IN .\Objects\Key.obj
COMPILER INVOKED BY: E:\Keil\C51\BIN\C51.EXE Key.c OPTIMIZE(8,SPEED) BROWSE DEBUG OBJECTEXTEND PRINT(.\Listings\Key.lst)
                    - OBJECT(.\Objects\Key.obj)

line level    source

   1          #include <REG52.H>
   2          #include <Key.h>
   3          
   4          unsigned char Key_Read()
   5          {
   6   1              unsigned char temp = 0;
   7   1      
   8   1              if(k1 == 0) temp = 1;
   9   1              if(k2 == 0) temp = 2;
  10   1              if(k3 == 0) temp = 3;
  11   1              if(k4 == 0) temp = 4;
  12   1              return temp;
  13   1      }


MODULE INFORMATION:   STATIC OVERLAYABLE
   CODE SIZE        =     22    ----
   CONSTANT SIZE    =   ----    ----
   XDATA SIZE       =   ----    ----
   PDATA SIZE       =   ----    ----
   DATA SIZE        =   ----    ----
   IDATA SIZE       =   ----    ----
   BIT SIZE         =   ----    ----
END OF MODULE INFORMATION.


C51 COMPILATION COMPLETE.  0 WARNING(S),  0 ERROR(S)
