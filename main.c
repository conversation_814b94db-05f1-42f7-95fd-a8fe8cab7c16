#include <reg52.h>
//#include <adc3085.h>
#include <lcd9648.h>
//#include <uart3085.h>
#include <key.h>


unsigned int Timer0_count = 0;   // ��ʱ��������
unsigned char Time_Flag = 0;     // 6�뵽���־
unsigned char Time_Flag_Count;   // ��ʱ���ʹ�����������
unsigned char LCD_Init_Mode = 1; //ģʽ1����ʾ����ѧ�Ű༶ ģʽ2����ʾ���ܽ��� ģʽ3��������ʾ
bit LCD_Init_End_Flag = 0; //��ʼ��ʾ������־λ

unsigned char Key_Slow_Down = 0;
unsigned char Key_Val,Key_Up,Key_Down,Key_Old;

unsigned char LCD_Disp_Mode = 0; // 0��LCD��ʾ���桡����LCD���ý���
unsigned char Temperature;//ʵʱ�¶ȱ���
unsigned char State_Index = 0;//״̬��ʾ��������ָ��

// �޸�ȫ�ֱ������岿��
unsigned int State_Disp[4] = {300,100,100,10}; // ��Ϊunsigned int���洢�Ŵ�10����ֵ
// NTC��Դ�����¶�(30.0��C)/DS18B20���±������¶�(10.0��C)/�����ֵ��ѹ(10.0V)/��ֵ�������(10A����������)

// ����ת��С���ַ�������
void Float_To_Str(unsigned int num, unsigned char *str, unsigned char unit, unsigned char decimal_flag)
{
    if(decimal_flag) // ��Ҫ��ʾС��
    {
        str[0] = (num / 100) + '0';        // ʮλ
        str[1] = (num / 10) % 10 + '0';    // ��λ  
        str[2] = '.';                      // С����
        str[3] = num % 10 + '0';           // С��λ
        str[4] = unit;                     // ��λ
        str[5] = '\0';
    }
    else // ������ʾ
    {
        str[0] = num / 10 + '0';
        str[1] = num % 10 + '0';
        str[2] = unit;
        str[3] = '\0';
    }
}

/*��������*/
void Key_Proc()
{
    if(Key_Slow_Down) return;
    Key_Slow_Down = 1;
    
    Key_Val = Key_Read();
    Key_Down = Key_Val & (Key_Old ^ Key_Val);
    Key_Up = ~Key_Val & (Key_Old ^ Key_Val);    
    Key_Old = Key_Val;
    
    switch(Key_Down)
    {
        case 1://����
            if(LCD_Disp_Mode == 1)
            {
                if(State_Index == 0)
                {   //NTC��Դ�����¶��趨 (25.0-85.0��C������0.1)
                    State_Disp[State_Index] += 1; // ����0.1
                    if(State_Disp[State_Index] > 850) // ����85.0
                        State_Disp[State_Index] = 850;    
                }
                
                if(State_Index == 1)
                {   //DS18B20���±������¶��趨 (20.0-90.0��C������0.1)
                    State_Disp[State_Index] += 1; // ����0.1
                    if(State_Disp[State_Index] > 900) // ����90.0
                        State_Disp[State_Index] = 900;    
                }
                
                if(State_Index == 2)
                {   //�����ֵ��ѹ�趨 (5.0-24.0V������0.1)
                    State_Disp[State_Index] += 1; // ����0.1
                    if(State_Disp[State_Index] > 240) // ����24.0V
                        State_Disp[State_Index] = 240;    
                }
                
                if(State_Index == 3)
                {   //��ֵ��������趨 (1-50A������1����������)
                    if(++State_Disp[State_Index] > 50)
                        State_Disp[State_Index] = 50;    
                }
            }
        break;
        
        case 2://�Լ�
            if(LCD_Disp_Mode == 1)
            {
                if(State_Index == 0)
                {   //NTC��Դ�����¶��趨 (25.0-85.0��C)
                    if(State_Disp[State_Index] > 250) // ����25.0
                        State_Disp[State_Index] -= 1; // ����0.1
                    else
                        State_Disp[State_Index] = 250; // ��С25.0
                }
                
                if(State_Index == 1)
                {   //DS18B20���±������¶��趨 (20.0-90.0��C)
                    if(State_Disp[State_Index] > 200) // ����20.0
                        State_Disp[State_Index] -= 1; // ����0.1
                    else
                        State_Disp[State_Index] = 200; // ��С20.0
                }
                
                if(State_Index == 2)
                {   //�����ֵ��ѹ�趨 (5.0-24.0V)
                    if(State_Disp[State_Index] > 50) // ����5.0V
                        State_Disp[State_Index] -= 1; // ����0.1
                    else
                        State_Disp[State_Index] = 50; // ��С5.0V
                }
                
                if(State_Index == 3)
                {   //��ֵ��������趨 (1-50A)
                    if(State_Disp[State_Index] > 1)
                        State_Disp[State_Index] -= 1;
                    else
                        State_Disp[State_Index] = 1; // ��С1A
                }
            }
        break;
        
        case 3://�����л�
            if(++LCD_Disp_Mode == 2) LCD_Disp_Mode = 0;
        break;
        
        case 4://����ָ������
            if(LCD_Disp_Mode == 1)
            {
                if(++State_Index == 4)
                    State_Index = 0;
            }    
        break;
    }
}





/*-----------------------------------------------------------------------------
 * ��������: Timer0_Init
 * ��������: ��ʱ��0��ʼ����1ms�ж�һ��
 *----------------------------------------------------------------------------*/
void Timer0_Init(void)
{
    TMOD &= 0xF0;    // �����ʱ��0ģʽλ
    TMOD |= 0x01;    // ��ʱ��0������ģʽ1��16λ��ʱ��
    TH0 = 0xFC;      // 1ms@11.0592MHz
    TL0 = 0x66;
    ET0 = 1;         // ʹ�ܶ�ʱ��0�ж�
    EA = 1;          // ʹ�����ж�
    TR0 = 1;         // ������ʱ��0
}

/*-----------------------------------------------------------------------------
 * ��������: Timer0_ISR
 * ��������: ��ʱ��0�жϷ�����
 *----------------------------------------------------------------------------*/
void Timer0_ISR(void) interrupt 1
{
    TH0 = 0xFC;      // ��װ��ֵ
    TL0 = 0x66;
    
    Timer0_count++;   // ����������
    
    if(Timer0_count >= 6000)  // 6000ms = 6��
    {
        Time_Flag = 1;       // ����6�뵽���־
        Timer0_count = 0;     // ���ü�����

    }
	
	if(++Key_Slow_Down == 10) Key_Slow_Down = 0;
}
void LCD9648_Init_Proc()
{
	switch(LCD_Init_Mode)
	{
		case 1:
			LCD9648_Init();
			LCD9648_Clear();
			
			LCD9648_Write16CnCHAR(0,0,"��");
			LCD9648_Write16CnCHAR(16,0,"��");
			LCD9648_Write16CnCHAR(32,0,"��");
		
			LCD9648_Write16EnCHAR(0,2,"202303103085");
			
			LCD9648_Write16CnCHAR(0,4,"��");
			LCD9648_Write16CnCHAR(16,4,"��");
			LCD9648_Write16EnCHAR(32,4,"23");
			LCD9648_Write16EnCHAR(48,4,"4");
			LCD9648_Write16CnCHAR(56,4,"��");
		break;
		
		case 2:
			LCD9648_Init();
			LCD9648_Clear();
			
			LCD9648_Write16CnCHAR(0,0,"��");
			LCD9648_Write16CnCHAR(16,0,"Я");
			LCD9648_Write16CnCHAR(32,0,"ʽ");
			LCD9648_Write16CnCHAR(48,0,"��");
			LCD9648_Write16CnCHAR(64,0,"��");
			LCD9648_Write16CnCHAR(80,0,"��");
			
			LCD9648_Write16EnCHAR(0,2,"25");//x=0,y=4,���ַ�
			LCD9648_Write16CnCHAR(16,2,"��");
			LCD9648_Write16EnCHAR(32,2,"07");
			LCD9648_Write16CnCHAR(48,2,"��");
			LCD9648_Write16EnCHAR(64,2,"11");
			LCD9648_Write16CnCHAR(80,2,"��");
		
			LCD9648_Write16CnCHAR(0,4,"��");
			LCD9648_Write16CnCHAR(16,4,"��");
			LCD9648_Write16EnCHAR(32,4,"1.");
			LCD9648_Write16EnCHAR(48,4,"23");
		break;
		
		case 3: //������ʾģʽ
            LCD9648_Init();
            LCD9648_Clear();
//            LCD9648_Write16CnCHAR(0,0,"��������ģʽ");
//            LCD9648_Write16CnCHAR(0,2,"�¶ȼ����...");
//			LCD9648_Write16EnCHAR(0,0,"#:");
//			LCD9648_Write16EnCHAR(0,2,"#:");
//			LCD9648_Write16EnCHAR(0,4,"#:");
//			LCD9648_Write16EnCHAR(0,4,"#:");
		
        break;
		
		default:
			
		break;
	}	
}

void LCD_Init_Test() //��ʼ״̬ʱ����⺯��
{
	if(Time_Flag)     //6��ʱ�䵽
	{
		Time_Flag = 0;
		Time_Flag_Count += 1;
		LCD_Init_Mode++; //�л�����һ��ģʽ
		if(LCD_Init_Mode <= 3)
		{
			LCD9648_Init_Proc(); //������ʾ
		}
		if(Time_Flag_Count == 2)
			LCD_Init_End_Flag ^= 1;
	}
	
}

//void LCD_Proc()
//{
//	State_Disp[State_Index]
//	LCD9648_Write16EnCHAR(4,0,"");
//}

void LCD_Proc()
{
    unsigned char temp_str[6]; // ���ӻ�������С������С��
    unsigned char i;
    
    if(LCD_Init_End_Flag)
    {
        if(LCD_Disp_Mode == 0) // LCD��ʾ����
        {
            // ����
            LCD9648_Clear();
            
            // ��ʾ�̶���ǩ
            LCD9648_Write16EnCHAR(0,0,"NTC:");     // NTC��Դ�����¶ȱ�ǩ
            LCD9648_Write16EnCHAR(0,2,"SET:");     // DS18B20���±������¶ȱ�ǩ  
            LCD9648_Write16EnCHAR(0,4,"VOL:");     // �����ֵ��ѹ��ǩ
            
            // ��ʾ����ֵ����С����
            // NTC��Դ�����¶�
            Float_To_Str(State_Disp[0], temp_str, 'C', 1);
            LCD9648_Write16EnCHAR(32,0,temp_str);
            
            // DS18B20���±������¶�
            Float_To_Str(State_Disp[1], temp_str, 'C', 1);
            LCD9648_Write16EnCHAR(32,2,temp_str);
            
            // �����ֵ��ѹ
            Float_To_Str(State_Disp[2], temp_str, 'V', 1);
            LCD9648_Write16EnCHAR(32,4,temp_str);
        }
        else if(LCD_Disp_Mode == 1) // LCD���ý���
        {
            // ����
            LCD9648_Clear();
            
            // ��ʾ���ý��棬ͻ����ʾ��ǰѡ����
            for(i = 0; i < 3; i++) // ֻ��ʾǰ3��
            {
                if(i == State_Index)
                {
                    LCD9648_Write16EnCHAR(0, i*2, ">"); // ��ʾѡ�б��
                }
                else
                {
                    LCD9648_Write16EnCHAR(0, i*2, " "); // ���ѡ�б��
                }
            }
            
            // ��ʾ��ǩ����ֵ
            LCD9648_Write16EnCHAR(8,0,"NTC:");
            Float_To_Str(State_Disp[0], temp_str, 'C', 1);
            LCD9648_Write16EnCHAR(40,0,temp_str);
            
            LCD9648_Write16EnCHAR(8,2,"SET:");
            Float_To_Str(State_Disp[1], temp_str, 'C', 1);
            LCD9648_Write16EnCHAR(40,2,temp_str);
            
            LCD9648_Write16EnCHAR(8,4,"VOL:");
            Float_To_Str(State_Disp[2], temp_str, 'V', 1);
            LCD9648_Write16EnCHAR(40,4,temp_str);
        }
    }
}


void main()
{
    LCD9648_Init_Proc(); //显示初始界面
    Timer0_Init();        //启动定时器

    while(1)
    {
        LCD_Init_Test();

		Key_Proc();
		LCD_Proc();
    }
}
