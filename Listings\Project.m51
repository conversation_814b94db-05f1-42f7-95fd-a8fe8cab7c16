BL<PERSON> BANKED LINKER/LOCATER V6.22                                                        07/07/2025  21:22:47  PAGE 1


BL51 BANKED LINKER/LOCATER V6.22, INVOKED BY:
E:\KEIL\C51\BIN\BL51.EXE .\Objects\main.obj, .\Objects\ds18b20.obj, .\Objects\lcd9648.obj, .\Objects\Key.obj TO .\Object
>> s\Project PRINT (.\Listings\Project.m51) RAMSIZE (256)


MEMORY MODEL: SMALL WITH FLOATING POINT ARITHMETIC


INPUT MODULES INCLUDED:
  .\Objects\main.obj (MAIN)
  .\Objects\ds18b20.obj (DS18B20)
  .\Objects\lcd9648.obj (LCD9648)
  .\Objects\Key.obj (KEY)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?FPMUL)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?FCAST)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?FPGETOPN)
  E:\KEIL\C51\LIB\C51S.LIB (?C_STARTUP)
  E:\KEIL\C51\LIB\C51S.LIB (?C?CLDPTR)
  E:\KEIL\C51\LIB\C51S.LIB (?C?CLDOPTR)
  E:\KEIL\C51\LIB\C51S.LIB (?C?LNEG)
  E:\KEIL\C51\LIB\C51S.LIB (?C_INIT)


LINK MAP OF MODULE:  .\Objects\Project (MAIN)


            TYPE    BASE      LENGTH    RELOCATION   SEGMENT NAME
            -----------------------------------------------------

            * * * * * * *   D A T A   M E M O R Y   * * * * * * *
            REG     0000H     0008H     ABSOLUTE     "REG BANK 0"
            DATA    0008H     0010H     UNIT         ?DT?MAIN
            DATA    0018H     0006H     UNIT         ?DT?DS18B20_READ_TEMPERTURE?DS18B20
            DATA    001EH     0004H     UNIT         ?DT?_LCD9648_WRITE12CNCHAR?LCD9648
            DATA    0022H     0004H     UNIT         ?DT?_LCD9648_WRITE12ENCHAR?LCD9648
            DATA    0026H     0004H     UNIT         _DATA_GROUP_
            DATA    002AH     0002H     UNIT         ?DT?_LCD9648_WRITE16ENCHAR1?LCD9648
            DATA    002CH     0001H     UNIT         ?DT?_LCD9648_WRITE12ENCHAR1?LCD9648
            IDATA   002DH     0001H     UNIT         ?STACK

            * * * * * * *   C O D E   M E M O R Y   * * * * * * *
            CODE    0000H     0003H     ABSOLUTE     
            CODE    0003H     0008H     UNIT         ?PR?DS18B20_START?DS18B20
            CODE    000BH     0003H     ABSOLUTE     
            CODE    000EH     097FH     UNIT         ?CO?LCD9648
            CODE    098DH     01E2H     UNIT         ?C?LIB_CODE
            CODE    0B6FH     0191H     UNIT         ?PR?LCD9648_INIT_PROC?MAIN
            CODE    0D00H     010DH     UNIT         ?PR?KEY_PROC?MAIN
            CODE    0E0DH     00C3H     UNIT         ?PR?_LCD9648_WRITE16CNCHAR?LCD9648
            CODE    0ED0H     00B9H     UNIT         ?PR?_LCD9648_WRITE12CNCHAR?LCD9648
            CODE    0F89H     00ACH     UNIT         ?PR?_LCD9648_WRITE16ENCHAR?LCD9648
            CODE    1035H     00A1H     UNIT         ?PR?_LCD9648_WRITE12ENCHAR?LCD9648
            CODE    10D6H     008EH     UNIT         ?PR?_LCD9648_WRITE16ENCHAR1?LCD9648
            CODE    1164H     008CH     UNIT         ?C_C51STARTUP
            CODE    11F0H     0083H     UNIT         ?PR?_LCD9648_WRITE12ENCHAR1?LCD9648
            CODE    1273H     0071H     UNIT         ?PR?DS18B20_READ_TEMPERTURE?DS18B20
            CODE    12E4H     0068H     UNIT         ?PR?LCD9648_INIT?LCD9648
            CODE    134CH     0067H     UNIT         ?CO?MAIN
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  21:22:47  PAGE 2


            CODE    13B3H     003BH     UNIT         ?PR?DS18B20_CHECK?DS18B20
            CODE    13EEH     0034H     UNIT         ?PR?_DS18B20_WRITE_BYTE?DS18B20
            CODE    1422H     0033H     UNIT         ?PR?TIMER0_ISR?MAIN
            CODE    1455H     002DH     UNIT         ?PR?LCD9648_CLEAR?LCD9648
            CODE    1482H     001BH     UNIT         ?PR?DS18B20_READ_BYTE?DS18B20
            CODE    149DH     001BH     UNIT         ?PR?_WRITEDATA?LCD9648
            CODE    14B8H     001AH     UNIT         ?C_INITSEG
            CODE    14D2H     001AH     UNIT         ?PR?DS18B20_READ_BIT?DS18B20
            CODE    14ECH     0019H     UNIT         ?PR?_SENDDATASPI?LCD9648
            CODE    1505H     0016H     UNIT         ?PR?KEY_READ?KEY
            CODE    151BH     0014H     UNIT         ?PR?LCD_INIT_TEST?MAIN
            CODE    152FH     0013H     UNIT         ?PR?TIMER0_INIT?MAIN
            CODE    1542H     0012H     UNIT         ?PR?_WRITECOMM?LCD9648
            CODE    1554H     0011H     UNIT         ?PR?MAIN?MAIN
            CODE    1565H     000FH     UNIT         ?PR?_DELAY_10US?DS18B20
            CODE    1574H     000EH     UNIT         ?PR?DS18B20_RESET?DS18B20
            CODE    1582H     0007H     UNIT         ?PR?DS18B20_INIT?DS18B20
            CODE    1589H     0001H     UNIT         ?PR?LCD_PROC?MAIN



OVERLAY MAP OF MODULE:   .\Objects\Project (MAIN)


SEGMENT                                       DATA_GROUP 
  +--> CALLED SEGMENT                       START    LENGTH
-----------------------------------------------------------
?C_C51STARTUP                               -----    -----
  +--> ?PR?MAIN?MAIN
  +--> ?C_INITSEG

?PR?MAIN?MAIN                               -----    -----
  +--> ?PR?LCD9648_INIT_PROC?MAIN
  +--> ?PR?TIMER0_INIT?MAIN
  +--> ?PR?LCD_INIT_TEST?MAIN
  +--> ?PR?KEY_PROC?MAIN
  +--> ?PR?LCD_PROC?MAIN

?PR?LCD9648_INIT_PROC?MAIN                  -----    -----
  +--> ?PR?LCD9648_INIT?LCD9648
  +--> ?PR?LCD9648_CLEAR?LCD9648
  +--> ?CO?MAIN
  +--> ?PR?_LCD9648_WRITE16CNCHAR?LCD9648
  +--> ?PR?_LCD9648_WRITE16ENCHAR?LCD9648

?PR?LCD9648_INIT?LCD9648                    -----    -----
  +--> ?PR?_WRITECOMM?LCD9648

?PR?_WRITECOMM?LCD9648                      -----    -----
  +--> ?PR?_SENDDATASPI?LCD9648

?PR?LCD9648_CLEAR?LCD9648                   -----    -----
  +--> ?PR?_WRITECOMM?LCD9648
  +--> ?PR?_WRITEDATA?LCD9648

?PR?_WRITEDATA?LCD9648                      -----    -----
  +--> ?PR?_SENDDATASPI?LCD9648
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  21:22:47  PAGE 3



?PR?_LCD9648_WRITE16CNCHAR?LCD9648          0026H    0004H
  +--> ?PR?_WRITECOMM?LCD9648
  +--> ?CO?LCD9648
  +--> ?PR?_WRITEDATA?LCD9648

?PR?_LCD9648_WRITE16ENCHAR?LCD9648          0026H    0004H
  +--> ?PR?_WRITECOMM?LCD9648
  +--> ?CO?LCD9648
  +--> ?PR?_WRITEDATA?LCD9648

?PR?LCD_INIT_TEST?MAIN                      -----    -----
  +--> ?PR?LCD9648_INIT_PROC?MAIN

?PR?KEY_PROC?MAIN                           -----    -----
  +--> ?PR?KEY_READ?KEY



UNRESOLVED EXTERNAL SYMBOLS:
   CN12CHARFONT
   EN12CHARFONT



SYMBOL TABLE OF MODULE:  .\Objects\Project (MAIN)

  VALUE           TYPE          NAME
  ----------------------------------

  -------         MODULE        MAIN
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0080H         PUBLIC        P0
  D:0090H         PUBLIC        P1
  D:00A0H         PUBLIC        P2
  D:00B0H         PUBLIC        P3
  C:1422H         PUBLIC        Timer0_ISR
  B:00A8H.7       PUBLIC        EA
  D:00A8H         PUBLIC        IE
  D:0008H         PUBLIC        Temperature
  D:00B8H         PUBLIC        IP
  C:0B6FH         PUBLIC        LCD9648_Init_Proc
  C:0D00H         PUBLIC        Key_Proc
  D:0009H         PUBLIC        Key_Down
  C:1554H         PUBLIC        main
  C:152FH         PUBLIC        Timer0_Init
  D:000AH         PUBLIC        Key_Old
  D:000BH         PUBLIC        Key_Slow_Down
  D:000CH         PUBLIC        LCD_Disp_Mode
  D:000DH         PUBLIC        Key_Val
  D:000EH         PUBLIC        LCD_Init_Mode
  D:0098H         PUBLIC        SCON
  D:0089H         PUBLIC        TMOD
  D:0088H         PUBLIC        TCON
  D:000FH         PUBLIC        Timer0_count
  B:00A8H.1       PUBLIC        ET0
  C:1589H         PUBLIC        LCD_Proc
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  21:22:47  PAGE 4


  D:008CH         PUBLIC        TH0
  C:151BH         PUBLIC        LCD_Init_Test
  D:0011H         PUBLIC        Key_Up
  D:008AH         PUBLIC        TL0
  B:0088H.4       PUBLIC        TR0
  D:0012H         PUBLIC        State_Index
  D:00C8H         PUBLIC        T2CON
  D:0013H         PUBLIC        Time_Flag
  D:0014H         PUBLIC        State_Disp
  D:00D0H         PUBLIC        PSW
  -------         PROC          KEY_PROC
  C:0D00H         LINE#         23
  C:0D00H         LINE#         24
  C:0D00H         LINE#         25
  C:0D07H         LINE#         26
  C:0D0AH         LINE#         28
  C:0D0FH         LINE#         29
  C:0D18H         LINE#         30
  C:0D1EH         LINE#         31
  C:0D21H         LINE#         33
  C:0D3AH         LINE#         34
  C:0D3AH         LINE#         35
  C:0D3AH         LINE#         36
  C:0D43H         LINE#         37
  C:0D43H         LINE#         38
  C:0D47H         LINE#         39
  C:0D47H         LINE#         40
  C:0D51H         LINE#         41
  C:0D58H         LINE#         42
  C:0D58H         LINE#         44
  C:0D5DH         LINE#         45
  C:0D5DH         LINE#         46
  C:0D65H         LINE#         47
  C:0D6CH         LINE#         48
  C:0D6CH         LINE#         50
  C:0D71H         LINE#         51
  C:0D71H         LINE#         52
  C:0D79H         LINE#         53
  C:0D80H         LINE#         54
  C:0D80H         LINE#         56
  C:0D89H         LINE#         57
  C:0D89H         LINE#         58
  C:0D94H         LINE#         59
  C:0D94H         LINE#         60
  C:0D94H         LINE#         61
  C:0D94H         LINE#         62
  C:0D96H         LINE#         64
  C:0D96H         LINE#         65
  C:0D9CH         LINE#         66
  C:0D9CH         LINE#         67
  C:0DA0H         LINE#         68
  C:0DA0H         LINE#         69
  C:0DAAH         LINE#         70
  C:0DB1H         LINE#         71
  C:0DB1H         LINE#         73
  C:0DB6H         LINE#         74
  C:0DB6H         LINE#         75
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  21:22:47  PAGE 5


  C:0DBEH         LINE#         76
  C:0DC5H         LINE#         77
  C:0DC5H         LINE#         79
  C:0DCAH         LINE#         80
  C:0DCAH         LINE#         81
  C:0DD2H         LINE#         82
  C:0DD9H         LINE#         83
  C:0DD9H         LINE#         85
  C:0DDFH         LINE#         86
  C:0DDFH         LINE#         87
  C:0DEAH         LINE#         88
  C:0DF1H         LINE#         89
  C:0DF1H         LINE#         90
  C:0DF1H         LINE#         91
  C:0DF2H         LINE#         93
  C:0DF2H         LINE#         94
  C:0DFCH         LINE#         95
  C:0DFDH         LINE#         97
  C:0DFDH         LINE#         98
  C:0E02H         LINE#         99
  C:0E02H         LINE#         100
  C:0E09H         LINE#         101
  C:0E0CH         LINE#         102
  C:0E0CH         LINE#         103
  C:0E0CH         LINE#         104
  C:0E0CH         LINE#         105
  -------         ENDPROC       KEY_PROC
  -------         PROC          TIMER0_INIT
  C:152FH         LINE#         116
  C:152FH         LINE#         117
  C:152FH         LINE#         118
  C:1532H         LINE#         119
  C:1535H         LINE#         120
  C:1538H         LINE#         121
  C:153BH         LINE#         122
  C:153DH         LINE#         123
  C:153FH         LINE#         124
  C:1541H         LINE#         125
  -------         ENDPROC       TIMER0_INIT
  -------         PROC          TIMER0_ISR
  C:1422H         LINE#         131
  C:1426H         LINE#         133
  C:1429H         LINE#         134
  C:142CH         LINE#         136
  C:1434H         LINE#         138
  C:143DH         LINE#         139
  C:143DH         LINE#         140
  C:1440H         LINE#         141
  C:1446H         LINE#         143
  C:1446H         LINE#         145
  C:1450H         LINE#         146
  -------         ENDPROC       TIMER0_ISR
  -------         PROC          LCD9648_INIT_PROC
  C:0B6FH         LINE#         147
  C:0B6FH         LINE#         148
  C:0B6FH         LINE#         149
  C:0B85H         LINE#         150
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  21:22:47  PAGE 6


  C:0B85H         LINE#         151
  C:0B85H         LINE#         152
  C:0B88H         LINE#         153
  C:0B8BH         LINE#         155
  C:0B97H         LINE#         156
  C:0BA4H         LINE#         157
  C:0BB1H         LINE#         159
  C:0BBEH         LINE#         161
  C:0BCBH         LINE#         162
  C:0BD8H         LINE#         163
  C:0BE5H         LINE#         164
  C:0BF2H         LINE#         165
  C:0BFFH         LINE#         166
  C:0BFFH         LINE#         168
  C:0BFFH         LINE#         169
  C:0C02H         LINE#         170
  C:0C05H         LINE#         172
  C:0C11H         LINE#         173
  C:0C1EH         LINE#         174
  C:0C2BH         LINE#         175
  C:0C38H         LINE#         176
  C:0C45H         LINE#         177
  C:0C52H         LINE#         179
  C:0C5FH         LINE#         180
  C:0C6CH         LINE#         181
  C:0C79H         LINE#         182
  C:0C86H         LINE#         183
  C:0C93H         LINE#         184
  C:0CA0H         LINE#         186
  C:0CADH         LINE#         187
  C:0CBAH         LINE#         188
  C:0CC7H         LINE#         189
  C:0CD1H         LINE#         190
  C:0CD3H         LINE#         192
  C:0CD3H         LINE#         193
  C:0CD6H         LINE#         194
  C:0CD9H         LINE#         197
  C:0CE5H         LINE#         198
  C:0CF2H         LINE#         199
  C:0CFFH         LINE#         200
  C:0CFFH         LINE#         202
  C:0CFFH         LINE#         204
  C:0CFFH         LINE#         205
  C:0CFFH         LINE#         206
  -------         ENDPROC       LCD9648_INIT_PROC
  -------         PROC          LCD_INIT_TEST
  C:151BH         LINE#         208
  C:151BH         LINE#         209
  C:151BH         LINE#         210
  C:151FH         LINE#         211
  C:151FH         LINE#         212
  C:1522H         LINE#         213
  C:1524H         LINE#         214
  C:152BH         LINE#         215
  C:152BH         LINE#         216
  C:152EH         LINE#         217
  C:152EH         LINE#         218
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  21:22:47  PAGE 7


  C:152EH         LINE#         219
  -------         ENDPROC       LCD_INIT_TEST
  -------         PROC          LCD_PROC
  C:1589H         LINE#         221
  C:1589H         LINE#         222
  C:1589H         LINE#         224
  -------         ENDPROC       LCD_PROC
  -------         PROC          MAIN
  C:1554H         LINE#         227
  C:1554H         LINE#         228
  C:1554H         LINE#         229
  C:1557H         LINE#         230
  C:155AH         LINE#         232
  C:155AH         LINE#         233
  C:155AH         LINE#         234
  C:155DH         LINE#         236
  C:1560H         LINE#         237
  C:1563H         LINE#         238
  -------         ENDPROC       MAIN
  -------         ENDMOD        MAIN

  -------         MODULE        DS18B20
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  C:1574H         PUBLIC        ds18b20_reset
  D:00A8H         PUBLIC        IE
  C:0003H         PUBLIC        ds18b20_start
  D:00B8H         PUBLIC        IP
  C:1482H         PUBLIC        ds18b20_read_byte
  C:1582H         PUBLIC        ds18b20_init
  C:14D2H         PUBLIC        ds18b20_read_bit
  C:13F6H         PUBLIC        _ds18b20_write_byte
  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
  C:1273H         PUBLIC        ds18b20_read_temperture
  C:1565H         PUBLIC        _delay_10us
  B:00B0H.7       PUBLIC        DS18B20_PORT
  D:00C8H         PUBLIC        T2CON
  C:13B3H         PUBLIC        ds18b20_check
  D:00D0H         PUBLIC        PSW
  -------         PROC          _DELAY_10US
  D:0007H         SYMBOL        xms
  -------         DO            
  D:0006H         SYMBOL        i
  -------         ENDDO         
  C:1565H         LINE#         4
  C:1565H         LINE#         5
  C:1565H         LINE#         7
  C:156BH         LINE#         8
  C:156BH         LINE#         9
  C:156DH         LINE#         10
  C:1573H         LINE#         12
  -------         ENDPROC       _DELAY_10US
  -------         PROC          DS18B20_RESET
  C:1574H         LINE#         20
  C:1574H         LINE#         21
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  21:22:47  PAGE 8


  C:1574H         LINE#         22
  C:1576H         LINE#         23
  C:157BH         LINE#         24
  C:157DH         LINE#         25
  -------         ENDPROC       DS18B20_RESET
  -------         PROC          DS18B20_CHECK
  -------         DO            
  D:0004H         SYMBOL        time_temp
  -------         ENDDO         
  C:13B3H         LINE#         34
  C:13B3H         LINE#         35
  C:13B3H         LINE#         36
  C:13B5H         LINE#         38
  C:13BEH         LINE#         39
  C:13BEH         LINE#         40
  C:13BFH         LINE#         41
  C:13C4H         LINE#         42
  C:13C6H         LINE#         43
  C:13CFH         LINE#         44
  C:13D1H         LINE#         45
  C:13DAH         LINE#         46
  C:13DAH         LINE#         47
  C:13DBH         LINE#         48
  C:13E0H         LINE#         49
  C:13E2H         LINE#         50
  C:13EBH         LINE#         51
  C:13EDH         LINE#         52
  -------         ENDPROC       DS18B20_CHECK
  -------         PROC          DS18B20_READ_BIT
  -------         DO            
  D:0004H         SYMBOL        dat
  -------         ENDDO         
  C:14D2H         LINE#         60
  C:14D2H         LINE#         61
  C:14D2H         LINE#         62
  C:14D4H         LINE#         64
  C:14D6H         LINE#         65
  C:14D8H         LINE#         66
  C:14DAH         LINE#         67
  C:14DCH         LINE#         68
  C:14E2H         LINE#         69
  C:14E4H         LINE#         70
  C:14E9H         LINE#         71
  C:14EBH         LINE#         72
  -------         ENDPROC       DS18B20_READ_BIT
  -------         PROC          DS18B20_READ_BYTE
  -------         DO            
  D:0003H         SYMBOL        i
  D:0002H         SYMBOL        dat
  D:0007H         SYMBOL        temp
  -------         ENDDO         
  C:1482H         LINE#         80
  C:1482H         LINE#         81
  C:1482H         LINE#         82
  C:1484H         LINE#         83
  C:1485H         LINE#         84
  C:1486H         LINE#         86
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  21:22:47  PAGE 9


  C:1486H         LINE#         87
  C:1486H         LINE#         88
  C:1489H         LINE#         89
  C:1496H         LINE#         90
  C:149AH         LINE#         91
  C:149CH         LINE#         92
  -------         ENDPROC       DS18B20_READ_BYTE
  -------         PROC          L?0035
  -------         ENDPROC       L?0035
  -------         PROC          _DS18B20_WRITE_BYTE
  D:0004H         SYMBOL        dat
  -------         DO            
  D:0003H         SYMBOL        i
  D:0002H         SYMBOL        temp
  -------         ENDDO         
  C:13F6H         LINE#         100
  C:13F8H         LINE#         101
  C:13F8H         LINE#         102
  C:13FAH         LINE#         103
  C:13FAH         LINE#         105
  C:13FAH         LINE#         106
  C:13FAH         LINE#         107
  C:13FEH         LINE#         108
  C:1402H         LINE#         109
  C:1405H         LINE#         110
  C:1405H         LINE#         111
  C:1407H         LINE#         112
  C:1409H         LINE#         113
  C:140BH         LINE#         114
  C:1410H         LINE#         115
  C:1412H         LINE#         117
  C:1412H         LINE#         118
  C:1414H         LINE#         119
  C:1419H         LINE#         120
  C:141BH         LINE#         121
  C:141DH         LINE#         122
  C:141DH         LINE#         123
  C:1421H         LINE#         124
  -------         ENDPROC       _DS18B20_WRITE_BYTE
  -------         PROC          DS18B20_START
  C:0003H         LINE#         132
  C:0003H         LINE#         133
  C:0003H         LINE#         134
  C:0003H         LINE#         135
  C:0003H         LINE#         136
  C:0006H         LINE#         137
  -------         ENDPROC       DS18B20_START
  -------         PROC          DS18B20_INIT
  C:1582H         LINE#         146
  C:1582H         LINE#         147
  C:1582H         LINE#         148
  C:1585H         LINE#         149
  C:1588H         LINE#         150
  -------         ENDPROC       DS18B20_INIT
  -------         PROC          DS18B20_READ_TEMPERTURE
  -------         DO            
  D:0018H         SYMBOL        temp
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  21:22:47  PAGE 10


  D:0007H         SYMBOL        dath
  D:0001H         SYMBOL        datl
  D:001CH         SYMBOL        value
  -------         ENDDO         
  C:1273H         LINE#         158
  C:1273H         LINE#         159
  C:1273H         LINE#         161
  C:1275H         LINE#         162
  C:1276H         LINE#         163
  C:127AH         LINE#         165
  C:127DH         LINE#         166
  C:127DH         LINE#         167
  C:127DH         LINE#         168
  C:1280H         LINE#         169
  C:1285H         LINE#         171
  C:128AH         LINE#         172
  C:128DH         LINE#         173
  C:1299H         LINE#         175
  C:12A0H         LINE#         176
  C:12A0H         LINE#         177
  C:12B1H         LINE#         178
  C:12BFH         LINE#         179
  C:12C1H         LINE#         181
  C:12C1H         LINE#         182
  C:12DBH         LINE#         183
  C:12DBH         LINE#         184
  C:12E3H         LINE#         185
  -------         ENDPROC       DS18B20_READ_TEMPERTURE
  -------         ENDMOD        DS18B20

  -------         MODULE        LCD9648
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0080H         PUBLIC        P0
  D:0090H         PUBLIC        P1
  D:00A0H         PUBLIC        P2
  D:00B0H         PUBLIC        P3
  C:11F0H         PUBLIC        _LCD9648_Write12EnCHAR1
  C:14AEH         PUBLIC        _WriteData
  C:10D6H         PUBLIC        _LCD9648_Write16EnCHAR1
  D:00A8H         PUBLIC        IE
  D:00B8H         PUBLIC        IP
  C:154AH         PUBLIC        _WriteComm
  B:00A0H.7       PUBLIC        RS
  C:12E4H         PUBLIC        LCD9648_Init
  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
  B:0080H.0       PUBLIC        CS0
  C:000EH         PUBLIC        CN16CHARfont
  C:033EH         PUBLIC        EN16CHARfont
  C:0ED0H         PUBLIC        _LCD9648_Write12CnCHAR
  C:1035H         PUBLIC        _LCD9648_Write12EnCHAR
  B:00A0H.5       PUBLIC        SDA
  C:0E0DH         PUBLIC        _LCD9648_Write16CnCHAR
  C:0F89H         PUBLIC        _LCD9648_Write16EnCHAR
  B:00A0H.6       PUBLIC        SCL
  D:00C8H         PUBLIC        T2CON
  C:14ECH         PUBLIC        _SendDataSPI
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  21:22:47  PAGE 11


  B:0080H.1       PUBLIC        RST
  C:1455H         PUBLIC        LCD9648_Clear
  D:00D0H         PUBLIC        PSW
  -------         PROC          _SENDDATASPI
  D:0007H         SYMBOL        dat
  -------         DO            
  D:0006H         SYMBOL        i
  -------         ENDDO         
  C:14ECH         LINE#         4
  C:14ECH         LINE#         5
  C:14ECH         LINE#         8
  C:14EEH         LINE#         9
  C:14EEH         LINE#         10
  C:14F6H         LINE#         11
  C:14F8H         LINE#         13
  C:14FCH         LINE#         15
  C:14FEH         LINE#         16
  C:1500H         LINE#         17
  C:1504H         LINE#         18
  -------         ENDPROC       _SENDDATASPI
  C:1544H         SYMBOL        L?0104
  C:1546H         SYMBOL        L?0105
  -------         PROC          L?0103
  -------         ENDPROC       L?0103
  C:1544H         SYMBOL        L?0104
  C:1546H         SYMBOL        L?0105
  -------         PROC          _WRITECOMM
  D:0006H         SYMBOL        i
  C:154AH         LINE#         20
  C:154AH         LINE#         21
  C:154AH         LINE#         23
  C:154CH         LINE#         24
  C:154EH         LINE#         26
  C:1551H         LINE#         28
  C:1553H         LINE#         30
  -------         ENDPROC       _WRITECOMM
  C:14A2H         SYMBOL        L?0107
  -------         PROC          L?0106
  -------         ENDPROC       L?0106
  C:14A2H         SYMBOL        L?0107
  -------         PROC          _WRITEDATA
  D:0006H         SYMBOL        i
  C:14AEH         LINE#         31
  C:14AEH         LINE#         32
  C:14AEH         LINE#         33
  C:14B0H         LINE#         34
  C:14B2H         LINE#         36
  C:14B5H         LINE#         38
  C:14B7H         LINE#         39
  -------         ENDPROC       _WRITEDATA
  -------         PROC          LCD9648_INIT
  -------         DO            
  D:0006H         SYMBOL        i
  -------         ENDDO         
  C:12E4H         LINE#         41
  C:12E4H         LINE#         42
  C:12E4H         LINE#         46
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  21:22:47  PAGE 12


  C:12E6H         LINE#         47
  C:12F4H         LINE#         49
  C:12F6H         LINE#         50
  C:1304H         LINE#         52
  C:1306H         LINE#         53
  C:1314H         LINE#         55
  C:131BH         LINE#         56
  C:1322H         LINE#         57
  C:1329H         LINE#         58
  C:1330H         LINE#         59
  C:1337H         LINE#         60
  C:133EH         LINE#         61
  C:1345H         LINE#         62
  -------         ENDPROC       LCD9648_INIT
  -------         PROC          LCD9648_CLEAR
  -------         DO            
  D:0005H         SYMBOL        i
  D:0004H         SYMBOL        j
  -------         ENDDO         
  C:1455H         LINE#         67
  C:1455H         LINE#         68
  C:1455H         LINE#         71
  C:1457H         LINE#         72
  C:1457H         LINE#         73
  C:145EH         LINE#         74
  C:1464H         LINE#         75
  C:146BH         LINE#         76
  C:1471H         LINE#         78
  C:1473H         LINE#         79
  C:1473H         LINE#         80
  C:1479H         LINE#         81
  C:147DH         LINE#         82
  C:1481H         LINE#         83
  -------         ENDPROC       LCD9648_CLEAR
  -------         PROC          _LCD9648_WRITE16CNCHAR
  D:0026H         SYMBOL        x
  D:0027H         SYMBOL        y
  D:0001H         SYMBOL        cn
  -------         DO            
  D:0004H         SYMBOL        j
  D:0028H         SYMBOL        x1
  D:0029H         SYMBOL        x2
  D:0005H         SYMBOL        wordNum
  -------         ENDDO         
  C:0E0DH         LINE#         86
  C:0E11H         LINE#         87
  C:0E11H         LINE#         91
  C:0E18H         LINE#         92
  C:0E18H         LINE#         93
  C:0E1BH         LINE#         94
  C:0E1BH         LINE#         97
  C:0E22H         LINE#         98
  C:0E22H         LINE#         99
  C:0E25H         LINE#         100
  C:0E25H         LINE#         101
  C:0E2BH         LINE#         103
  C:0E31H         LINE#         104
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  21:22:47  PAGE 13


  C:0E39H         LINE#         105
  C:0E39H         LINE#         108
  C:0E40H         LINE#         110
  C:0E47H         LINE#         111
  C:0E4DH         LINE#         113
  C:0E50H         LINE#         114
  C:0E57H         LINE#         115
  C:0E59H         LINE#         116
  C:0E59H         LINE#         118
  C:0E8CH         LINE#         120
  C:0E8CH         LINE#         121
  C:0E8DH         LINE#         122
  C:0E8DH         LINE#         123
  C:0E92H         LINE#         124
  C:0E92H         LINE#         126
  C:0E99H         LINE#         129
  C:0E9CH         LINE#         130
  C:0EA3H         LINE#         131
  C:0EA3H         LINE#         132
  C:0EB3H         LINE#         133
  C:0EB7H         LINE#         134
  C:0EBDH         LINE#         135
  C:0EBDH         LINE#         136
  C:0EC3H         LINE#         137
  C:0ECAH         LINE#         139
  C:0ECDH         LINE#         140
  C:0ECFH         LINE#         141
  -------         ENDPROC       _LCD9648_WRITE16CNCHAR
  -------         PROC          _LCD9648_WRITE16ENCHAR
  D:0026H         SYMBOL        x
  D:0027H         SYMBOL        y
  D:0001H         SYMBOL        en
  -------         DO            
  D:0004H         SYMBOL        j
  D:0028H         SYMBOL        x1
  D:0029H         SYMBOL        x2
  D:0005H         SYMBOL        wordNum
  -------         ENDDO         
  C:0F89H         LINE#         143
  C:0F8DH         LINE#         144
  C:0F8DH         LINE#         148
  C:0F94H         LINE#         149
  C:0F94H         LINE#         150
  C:0F97H         LINE#         151
  C:0F97H         LINE#         154
  C:0F9EH         LINE#         155
  C:0F9EH         LINE#         156
  C:0FA1H         LINE#         157
  C:0FA1H         LINE#         158
  C:0FA7H         LINE#         160
  C:0FADH         LINE#         161
  C:0FB5H         LINE#         162
  C:0FB5H         LINE#         165
  C:0FBCH         LINE#         167
  C:0FC3H         LINE#         168
  C:0FC9H         LINE#         170
  C:0FCEH         LINE#         171
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  21:22:47  PAGE 14


  C:0FD5H         LINE#         172
  C:0FD7H         LINE#         173
  C:0FD7H         LINE#         175
  C:0FEFH         LINE#         176
  C:0FEFH         LINE#         177
  C:0FF0H         LINE#         178
  C:0FF0H         LINE#         179
  C:0FF5H         LINE#         180
  C:0FF5H         LINE#         182
  C:0FFCH         LINE#         185
  C:1001H         LINE#         186
  C:1008H         LINE#         187
  C:1008H         LINE#         188
  C:1018H         LINE#         189
  C:101CH         LINE#         190
  C:1022H         LINE#         191
  C:1022H         LINE#         192
  C:1028H         LINE#         193
  C:102FH         LINE#         195
  C:1032H         LINE#         196
  C:1034H         LINE#         197
  -------         ENDPROC       _LCD9648_WRITE16ENCHAR
  -------         PROC          _LCD9648_WRITE16ENCHAR1
  D:0001H         SYMBOL        x
  D:0005H         SYMBOL        y
  D:0003H         SYMBOL        en
  -------         DO            
  D:0002H         SYMBOL        j
  D:002AH         SYMBOL        x1
  D:002BH         SYMBOL        x2
  D:0004H         SYMBOL        wordNum
  -------         ENDDO         
  C:10D6H         LINE#         198
  C:10D8H         LINE#         199
  C:10D8H         LINE#         203
  C:10DEH         LINE#         204
  C:10DEH         LINE#         205
  C:10E1H         LINE#         206
  C:10E1H         LINE#         209
  C:10E7H         LINE#         210
  C:10E7H         LINE#         211
  C:10EAH         LINE#         212
  C:10EAH         LINE#         213
  C:10EEH         LINE#         215
  C:10F4H         LINE#         217
  C:10F4H         LINE#         220
  C:10FBH         LINE#         222
  C:1101H         LINE#         223
  C:1106H         LINE#         225
  C:110BH         LINE#         226
  C:1112H         LINE#         227
  C:1114H         LINE#         228
  C:1114H         LINE#         230
  C:1128H         LINE#         231
  C:1128H         LINE#         232
  C:1129H         LINE#         233
  C:1129H         LINE#         234
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  21:22:47  PAGE 15


  C:112EH         LINE#         235
  C:112EH         LINE#         237
  C:1134H         LINE#         240
  C:1139H         LINE#         241
  C:1140H         LINE#         242
  C:1140H         LINE#         243
  C:1155H         LINE#         244
  C:1159H         LINE#         245
  C:115DH         LINE#         246
  C:115DH         LINE#         247
  C:1161H         LINE#         248
  C:1161H         LINE#         250
  C:1161H         LINE#         251
  C:1163H         LINE#         252
  -------         ENDPROC       _LCD9648_WRITE16ENCHAR1
  -------         PROC          _LCD9648_WRITE12CNCHAR
  D:001EH         SYMBOL        x
  D:001FH         SYMBOL        y
  D:0001H         SYMBOL        cn
  -------         DO            
  D:0004H         SYMBOL        j
  D:0020H         SYMBOL        x1
  D:0021H         SYMBOL        x2
  D:0005H         SYMBOL        wordNum
  -------         ENDDO         
  C:0ED0H         LINE#         260
  C:0ED4H         LINE#         261
  C:0ED4H         LINE#         264
  C:0EDEH         LINE#         265
  C:0EE8H         LINE#         267
  C:0EEEH         LINE#         268
  C:0EF4H         LINE#         270
  C:0EFCH         LINE#         271
  C:0F03H         LINE#         273
  C:0F0AH         LINE#         274
  C:0F10H         LINE#         275
  C:0F15H         LINE#         276
  C:0F1CH         LINE#         278
  C:0F1EH         LINE#         279
  C:0F41H         LINE#         280
  C:0F41H         LINE#         282
  C:0F42H         LINE#         283
  C:0F47H         LINE#         284
  C:0F4EH         LINE#         285
  C:0F53H         LINE#         286
  C:0F5AH         LINE#         287
  C:0F5AH         LINE#         288
  C:0F6AH         LINE#         289
  C:0F6EH         LINE#         290
  C:0F74H         LINE#         291
  C:0F76H         LINE#         292
  C:0F76H         LINE#         293
  C:0F7CH         LINE#         294
  C:0F83H         LINE#         295
  C:0F86H         LINE#         296
  C:0F88H         LINE#         297
  -------         ENDPROC       _LCD9648_WRITE12CNCHAR
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  21:22:47  PAGE 16


  -------         PROC          _LCD9648_WRITE12ENCHAR
  D:0022H         SYMBOL        x
  D:0023H         SYMBOL        y
  D:0001H         SYMBOL        en
  -------         DO            
  D:0004H         SYMBOL        j
  D:0024H         SYMBOL        x1
  D:0025H         SYMBOL        x2
  D:0005H         SYMBOL        wordNum
  -------         ENDDO         
  C:1035H         LINE#         305
  C:1039H         LINE#         306
  C:1039H         LINE#         309
  C:1043H         LINE#         310
  C:104DH         LINE#         312
  C:1053H         LINE#         313
  C:1059H         LINE#         315
  C:105EH         LINE#         316
  C:1065H         LINE#         318
  C:106CH         LINE#         319
  C:1072H         LINE#         320
  C:1077H         LINE#         321
  C:107EH         LINE#         323
  C:1080H         LINE#         324
  C:1090H         LINE#         326
  C:1091H         LINE#         327
  C:1096H         LINE#         328
  C:109DH         LINE#         329
  C:10A2H         LINE#         330
  C:10A9H         LINE#         331
  C:10A9H         LINE#         332
  C:10B9H         LINE#         333
  C:10BDH         LINE#         334
  C:10C3H         LINE#         335
  C:10C5H         LINE#         336
  C:10C5H         LINE#         337
  C:10C9H         LINE#         338
  C:10D0H         LINE#         339
  C:10D3H         LINE#         340
  C:10D5H         LINE#         341
  -------         ENDPROC       _LCD9648_WRITE12ENCHAR
  -------         PROC          _LCD9648_WRITE12ENCHAR1
  D:0004H         SYMBOL        x
  D:0005H         SYMBOL        y
  D:002CH         SYMBOL        en
  -------         DO            
  D:0002H         SYMBOL        j
  D:0001H         SYMBOL        x1
  D:0004H         SYMBOL        x2
  D:0003H         SYMBOL        wordNum
  -------         ENDDO         
  C:11F0H         LINE#         349
  C:11F4H         LINE#         350
  C:11F4H         LINE#         353
  C:11FDH         LINE#         354
  C:1206H         LINE#         356
  C:120AH         LINE#         357
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  21:22:47  PAGE 17


  C:1210H         LINE#         358
  C:1217H         LINE#         360
  C:121CH         LINE#         361
  C:1220H         LINE#         362
  C:1224H         LINE#         363
  C:122BH         LINE#         365
  C:1233H         LINE#         366
  C:1240H         LINE#         368
  C:1241H         LINE#         369
  C:1246H         LINE#         370
  C:124CH         LINE#         371
  C:1250H         LINE#         372
  C:1257H         LINE#         373
  C:1257H         LINE#         374
  C:1267H         LINE#         375
  C:126DH         LINE#         376
  C:126DH         LINE#         377
  C:126DH         LINE#         378
  C:1270H         LINE#         379
  C:1272H         LINE#         380
  -------         ENDPROC       _LCD9648_WRITE12ENCHAR1
  -------         ENDMOD        LCD9648

  -------         MODULE        KEY
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  C:1505H         PUBLIC        Key_Read
  D:00A8H         PUBLIC        IE
  D:00B8H         PUBLIC        IP
  B:0090H.0       PUBLIC        k1
  B:0090H.1       PUBLIC        k2
  B:0090H.2       PUBLIC        k3
  B:0090H.3       PUBLIC        k4
  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
  D:00C8H         PUBLIC        T2CON
  D:00D0H         PUBLIC        PSW
  -------         PROC          KEY_READ
  -------         DO            
  D:0007H         SYMBOL        temp
  -------         ENDDO         
  C:1505H         LINE#         4
  C:1505H         LINE#         5
  C:1505H         LINE#         6
  C:1507H         LINE#         8
  C:150BH         LINE#         9
  C:1510H         LINE#         10
  C:1515H         LINE#         11
  C:151AH         LINE#         12
  C:151AH         LINE#         13
  -------         ENDPROC       KEY_READ
  -------         ENDMOD        KEY

  -------         MODULE        ?C?FPMUL
  C:098DH         PUBLIC        ?C?FPMUL
  -------         ENDMOD        ?C?FPMUL
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  21:22:47  PAGE 18



  -------         MODULE        ?C?FCAST
  C:0A9DH         PUBLIC        ?C?FCASTC
  C:0A98H         PUBLIC        ?C?FCASTI
  C:0A93H         PUBLIC        ?C?FCASTL
  -------         ENDMOD        ?C?FCAST

  -------         MODULE        ?C?CLDPTR
  C:0B1BH         PUBLIC        ?C?CLDPTR
  -------         ENDMOD        ?C?CLDPTR

  -------         MODULE        ?C?CLDOPTR
  C:0B34H         PUBLIC        ?C?CLDOPTR
  -------         ENDMOD        ?C?CLDOPTR

  -------         MODULE        ?C?LNEG
  C:0B61H         PUBLIC        ?C?LNEG
  -------         ENDMOD        ?C?LNEG

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?DS18B20_INIT?DS18B20

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?DS18B20_READ_TEMPERTURE?DS18B20

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?_LCD9648_WRITE16ENCHAR1?LCD9648

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?_LCD9648_WRITE12CNCHAR?LCD9648

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?_LCD9648_WRITE12ENCHAR?LCD9648

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?_LCD9648_WRITE12ENCHAR1?LCD9648

*** WARNING L1: UNRESOLVED EXTERNAL SYMBOL
    SYMBOL:  CN12CHARFONT
    MODULE:  .\Objects\lcd9648.obj (LCD9648)

*** WARNING L1: UNRESOLVED EXTERNAL SYMBOL
    SYMBOL:  EN12CHARFONT
    MODULE:  .\Objects\lcd9648.obj (LCD9648)

*** WARNING L2: REFERENCE MADE TO UNRESOLVED EXTERNAL
    SYMBOL:  CN12CHARFONT
    MODULE:  .\Objects\lcd9648.obj (LCD9648)
    ADDRESS: 0F28H

*** WARNING L2: REFERENCE MADE TO UNRESOLVED EXTERNAL
    SYMBOL:  CN12CHARFONT
    MODULE:  .\Objects\lcd9648.obj (LCD9648)
    ADDRESS: 0F3BH

*** WARNING L2: REFERENCE MADE TO UNRESOLVED EXTERNAL
    SYMBOL:  CN12CHARFONT
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  21:22:47  PAGE 19


    MODULE:  .\Objects\lcd9648.obj (LCD9648)
    ADDRESS: 0F60H

*** WARNING L2: REFERENCE MADE TO UNRESOLVED EXTERNAL
    SYMBOL:  EN12CHARFONT
    MODULE:  .\Objects\lcd9648.obj (LCD9648)
    ADDRESS: 108AH

*** WARNING L2: REFERENCE MADE TO UNRESOLVED EXTERNAL
    SYMBOL:  EN12CHARFONT
    MODULE:  .\Objects\lcd9648.obj (LCD9648)
    ADDRESS: 10AFH

*** WARNING L2: REFERENCE MADE TO UNRESOLVED EXTERNAL
    SYMBOL:  EN12CHARFONT
    MODULE:  .\Objects\lcd9648.obj (LCD9648)
    ADDRESS: 1239H

*** WARNING L2: REFERENCE MADE TO UNRESOLVED EXTERNAL
    SYMBOL:  EN12CHARFONT
    MODULE:  .\Objects\lcd9648.obj (LCD9648)
    ADDRESS: 125DH

Program Size: data=46.0 xdata=0 code=5514
LINK/LOCATE RUN COMPLETE.  15 WARNING(S),  0 ERROR(S)
