BL51 BANKED LINKER/LOCATER V6.22                                                        07/07/2025  21:47:58  PAGE 1


BL51 BANKED LINKER/LOCATER V6.22, INVOKED BY:
E:\KEIL\C51\BIN\BL51.EXE .\Objects\main.obj, .\Objects\ds18b20.obj, .\Objects\lcd9648.obj, .\Objects\Key.obj TO .\Object
>> s\Project PRINT (.\Listings\Project.m51) RAMSIZE (256)


MEMORY MODEL: SMALL WITH FLOATING POINT ARITHMETIC


INPUT MODULES INCLUDED:
  .\Objects\main.obj (MAIN)
  .\Objects\ds18b20.obj (DS18B20)
  .\Objects\lcd9648.obj (LCD9648)
  .\Objects\Key.obj (KEY)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?FPMUL)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?FCAST)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?FPGETOPN)
  E:\KEIL\C51\LIB\C51S.LIB (?C_STARTUP)
  E:\KEIL\C51\LIB\C51S.LIB (?C?CLDPTR)
  E:\KEIL\C51\LIB\C51S.LIB (?C?CLDOPTR)
  E:\KEIL\C51\LIB\C51S.LIB (?C?LNEG)
  E:\KEIL\C51\LIB\C51S.LIB (?C_INIT)


LINK MAP OF MODULE:  .\Objects\Project (MAIN)


            TYPE    BASE      LENGTH    RELOCATION   SEGMENT NAME
            -----------------------------------------------------

            * * * * * * *   D A T A   M E M O R Y   * * * * * * *
            REG     0000H     0008H     ABSOLUTE     "REG BANK 0"
            DATA    0008H     0010H     UNIT         ?DT?MAIN
            DATA    0018H     0006H     UNIT         ?DT?DS18B20_READ_TEMPERTURE?DS18B20
            DATA    001EH     0004H     UNIT         _DATA_GROUP_
            DATA    0022H     0002H     UNIT         ?DT?_LCD9648_WRITE16ENCHAR1?LCD9648
            IDATA   0024H     0001H     UNIT         ?STACK

            * * * * * * *   C O D E   M E M O R Y   * * * * * * *
            CODE    0000H     0003H     ABSOLUTE     
            CODE    0003H     0008H     UNIT         ?PR?DS18B20_START?DS18B20
            CODE    000BH     0003H     ABSOLUTE     
            CODE    000EH     0891H     UNIT         ?CO?LCD9648
            CODE    089FH     01E2H     UNIT         ?C?LIB_CODE
            CODE    0A81H     0191H     UNIT         ?PR?LCD9648_INIT_PROC?MAIN
            CODE    0C12H     010DH     UNIT         ?PR?KEY_PROC?MAIN
            CODE    0D1FH     00C3H     UNIT         ?PR?_LCD9648_WRITE16CNCHAR?LCD9648
            CODE    0DE2H     00ACH     UNIT         ?PR?_LCD9648_WRITE16ENCHAR?LCD9648
            CODE    0E8EH     008EH     UNIT         ?PR?_LCD9648_WRITE16ENCHAR1?LCD9648
            CODE    0F1CH     008CH     UNIT         ?C_C51STARTUP
            CODE    0FA8H     0071H     UNIT         ?PR?DS18B20_READ_TEMPERTURE?DS18B20
            CODE    1019H     006CH     UNIT         ?CO?MAIN
            CODE    1085H     0068H     UNIT         ?PR?LCD9648_INIT?LCD9648
            CODE    10EDH     003BH     UNIT         ?PR?DS18B20_CHECK?DS18B20
            CODE    1128H     0034H     UNIT         ?PR?_DS18B20_WRITE_BYTE?DS18B20
            CODE    115CH     0033H     UNIT         ?PR?TIMER0_ISR?MAIN
            CODE    118FH     002DH     UNIT         ?PR?LCD9648_CLEAR?LCD9648
            CODE    11BCH     001BH     UNIT         ?PR?DS18B20_READ_BYTE?DS18B20
            CODE    11D7H     001BH     UNIT         ?PR?_WRITEDATA?LCD9648
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  21:47:58  PAGE 2


            CODE    11F2H     001AH     UNIT         ?C_INITSEG
            CODE    120CH     001AH     UNIT         ?PR?DS18B20_READ_BIT?DS18B20
            CODE    1226H     0019H     UNIT         ?PR?_SENDDATASPI?LCD9648
            CODE    123FH     0016H     UNIT         ?PR?KEY_READ?KEY
            CODE    1255H     0014H     UNIT         ?PR?LCD_INIT_TEST?MAIN
            CODE    1269H     0013H     UNIT         ?PR?TIMER0_INIT?MAIN
            CODE    127CH     0012H     UNIT         ?PR?_WRITECOMM?LCD9648
            CODE    128EH     0011H     UNIT         ?PR?MAIN?MAIN
            CODE    129FH     000FH     UNIT         ?PR?_DELAY_10US?DS18B20
            CODE    12AEH     000EH     UNIT         ?PR?DS18B20_RESET?DS18B20
            CODE    12BCH     000DH     UNIT         ?PR?LCD_PROC?MAIN
            CODE    12C9H     0007H     UNIT         ?PR?DS18B20_INIT?DS18B20



OVERLAY MAP OF MODULE:   .\Objects\Project (MAIN)


SEGMENT                                       DATA_GROUP 
  +--> CALLED SEGMENT                       START    LENGTH
-----------------------------------------------------------
?C_C51STARTUP                               -----    -----
  +--> ?PR?MAIN?MAIN
  +--> ?C_INITSEG

?PR?MAIN?MAIN                               -----    -----
  +--> ?PR?LCD9648_INIT_PROC?MAIN
  +--> ?PR?TIMER0_INIT?MAIN
  +--> ?PR?LCD_INIT_TEST?MAIN
  +--> ?PR?KEY_PROC?MAIN
  +--> ?PR?LCD_PROC?MAIN

?PR?LCD9648_INIT_PROC?MAIN                  -----    -----
  +--> ?PR?LCD9648_INIT?LCD9648
  +--> ?PR?LCD9648_CLEAR?LCD9648
  +--> ?CO?MAIN
  +--> ?PR?_LCD9648_WRITE16CNCHAR?LCD9648
  +--> ?PR?_LCD9648_WRITE16ENCHAR?LCD9648

?PR?LCD9648_INIT?LCD9648                    -----    -----
  +--> ?PR?_WRITECOMM?LCD9648

?PR?_WRITECOMM?LCD9648                      -----    -----
  +--> ?PR?_SENDDATASPI?LCD9648

?PR?LCD9648_CLEAR?LCD9648                   -----    -----
  +--> ?PR?_WRITECOMM?LCD9648
  +--> ?PR?_WRITEDATA?LCD9648

?PR?_WRITEDATA?LCD9648                      -----    -----
  +--> ?PR?_SENDDATASPI?LCD9648

?PR?_LCD9648_WRITE16CNCHAR?LCD9648          001EH    0004H
  +--> ?PR?_WRITECOMM?LCD9648
  +--> ?CO?LCD9648
  +--> ?PR?_WRITEDATA?LCD9648

BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  21:47:58  PAGE 3


?PR?_LCD9648_WRITE16ENCHAR?LCD9648          001EH    0004H
  +--> ?PR?_WRITECOMM?LCD9648
  +--> ?CO?LCD9648
  +--> ?PR?_WRITEDATA?LCD9648

?PR?LCD_INIT_TEST?MAIN                      -----    -----
  +--> ?PR?LCD9648_INIT_PROC?MAIN

?PR?KEY_PROC?MAIN                           -----    -----
  +--> ?PR?KEY_READ?KEY

?PR?LCD_PROC?MAIN                           -----    -----
  +--> ?CO?MAIN
  +--> ?PR?_LCD9648_WRITE16ENCHAR?LCD9648



SYMBOL TABLE OF MODULE:  .\Objects\Project (MAIN)

  VALUE           TYPE          NAME
  ----------------------------------

  -------         MODULE        MAIN
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0080H         PUBLIC        P0
  D:0090H         PUBLIC        P1
  D:00A0H         PUBLIC        P2
  D:00B0H         PUBLIC        P3
  C:115CH         PUBLIC        Timer0_ISR
  B:00A8H.7       PUBLIC        EA
  D:00A8H         PUBLIC        IE
  D:0008H         PUBLIC        Temperature
  D:00B8H         PUBLIC        IP
  C:0A81H         PUBLIC        LCD9648_Init_Proc
  C:0C12H         PUBLIC        Key_Proc
  D:0009H         PUBLIC        Key_Down
  C:128EH         PUBLIC        main
  C:1269H         PUBLIC        Timer0_Init
  D:000AH         PUBLIC        Key_Old
  D:000BH         PUBLIC        Key_Slow_Down
  D:000CH         PUBLIC        LCD_Disp_Mode
  D:000DH         PUBLIC        Key_Val
  D:000EH         PUBLIC        LCD_Init_Mode
  D:0098H         PUBLIC        SCON
  D:0089H         PUBLIC        TMOD
  D:0088H         PUBLIC        TCON
  D:000FH         PUBLIC        Timer0_count
  B:00A8H.1       PUBLIC        ET0
  C:12BCH         PUBLIC        LCD_Proc
  D:008CH         PUBLIC        TH0
  C:1255H         PUBLIC        LCD_Init_Test
  D:0011H         PUBLIC        Key_Up
  D:008AH         PUBLIC        TL0
  B:0088H.4       PUBLIC        TR0
  D:0012H         PUBLIC        State_Index
  D:00C8H         PUBLIC        T2CON
  D:0013H         PUBLIC        Time_Flag
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  21:47:58  PAGE 4


  D:0014H         PUBLIC        State_Disp
  D:00D0H         PUBLIC        PSW
  -------         PROC          KEY_PROC
  C:0C12H         LINE#         23
  C:0C12H         LINE#         24
  C:0C12H         LINE#         25
  C:0C19H         LINE#         26
  C:0C1CH         LINE#         28
  C:0C21H         LINE#         29
  C:0C2AH         LINE#         30
  C:0C30H         LINE#         31
  C:0C33H         LINE#         33
  C:0C4CH         LINE#         34
  C:0C4CH         LINE#         35
  C:0C4CH         LINE#         36
  C:0C55H         LINE#         37
  C:0C55H         LINE#         38
  C:0C59H         LINE#         39
  C:0C59H         LINE#         40
  C:0C63H         LINE#         41
  C:0C6AH         LINE#         42
  C:0C6AH         LINE#         44
  C:0C6FH         LINE#         45
  C:0C6FH         LINE#         46
  C:0C77H         LINE#         47
  C:0C7EH         LINE#         48
  C:0C7EH         LINE#         50
  C:0C83H         LINE#         51
  C:0C83H         LINE#         52
  C:0C8BH         LINE#         53
  C:0C92H         LINE#         54
  C:0C92H         LINE#         56
  C:0C9BH         LINE#         57
  C:0C9BH         LINE#         58
  C:0CA6H         LINE#         59
  C:0CA6H         LINE#         60
  C:0CA6H         LINE#         61
  C:0CA6H         LINE#         62
  C:0CA8H         LINE#         64
  C:0CA8H         LINE#         65
  C:0CAEH         LINE#         66
  C:0CAEH         LINE#         67
  C:0CB2H         LINE#         68
  C:0CB2H         LINE#         69
  C:0CBCH         LINE#         70
  C:0CC3H         LINE#         71
  C:0CC3H         LINE#         73
  C:0CC8H         LINE#         74
  C:0CC8H         LINE#         75
  C:0CD0H         LINE#         76
  C:0CD7H         LINE#         77
  C:0CD7H         LINE#         79
  C:0CDCH         LINE#         80
  C:0CDCH         LINE#         81
  C:0CE4H         LINE#         82
  C:0CEBH         LINE#         83
  C:0CEBH         LINE#         85
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  21:47:58  PAGE 5


  C:0CF1H         LINE#         86
  C:0CF1H         LINE#         87
  C:0CFCH         LINE#         88
  C:0D03H         LINE#         89
  C:0D03H         LINE#         90
  C:0D03H         LINE#         91
  C:0D04H         LINE#         93
  C:0D04H         LINE#         94
  C:0D0EH         LINE#         95
  C:0D0FH         LINE#         97
  C:0D0FH         LINE#         98
  C:0D14H         LINE#         99
  C:0D14H         LINE#         100
  C:0D1BH         LINE#         101
  C:0D1EH         LINE#         102
  C:0D1EH         LINE#         103
  C:0D1EH         LINE#         104
  C:0D1EH         LINE#         105
  -------         ENDPROC       KEY_PROC
  -------         PROC          TIMER0_INIT
  C:1269H         LINE#         116
  C:1269H         LINE#         117
  C:1269H         LINE#         118
  C:126CH         LINE#         119
  C:126FH         LINE#         120
  C:1272H         LINE#         121
  C:1275H         LINE#         122
  C:1277H         LINE#         123
  C:1279H         LINE#         124
  C:127BH         LINE#         125
  -------         ENDPROC       TIMER0_INIT
  -------         PROC          TIMER0_ISR
  C:115CH         LINE#         131
  C:1160H         LINE#         133
  C:1163H         LINE#         134
  C:1166H         LINE#         136
  C:116EH         LINE#         138
  C:1177H         LINE#         139
  C:1177H         LINE#         140
  C:117AH         LINE#         141
  C:1180H         LINE#         143
  C:1180H         LINE#         145
  C:118AH         LINE#         146
  -------         ENDPROC       TIMER0_ISR
  -------         PROC          LCD9648_INIT_PROC
  C:0A81H         LINE#         147
  C:0A81H         LINE#         148
  C:0A81H         LINE#         149
  C:0A97H         LINE#         150
  C:0A97H         LINE#         151
  C:0A97H         LINE#         152
  C:0A9AH         LINE#         153
  C:0A9DH         LINE#         155
  C:0AA9H         LINE#         156
  C:0AB6H         LINE#         157
  C:0AC3H         LINE#         159
  C:0AD0H         LINE#         161
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  21:47:58  PAGE 6


  C:0ADDH         LINE#         162
  C:0AEAH         LINE#         163
  C:0AF7H         LINE#         164
  C:0B04H         LINE#         165
  C:0B11H         LINE#         166
  C:0B11H         LINE#         168
  C:0B11H         LINE#         169
  C:0B14H         LINE#         170
  C:0B17H         LINE#         172
  C:0B23H         LINE#         173
  C:0B30H         LINE#         174
  C:0B3DH         LINE#         175
  C:0B4AH         LINE#         176
  C:0B57H         LINE#         177
  C:0B64H         LINE#         179
  C:0B71H         LINE#         180
  C:0B7EH         LINE#         181
  C:0B8BH         LINE#         182
  C:0B98H         LINE#         183
  C:0BA5H         LINE#         184
  C:0BB2H         LINE#         186
  C:0BBFH         LINE#         187
  C:0BCCH         LINE#         188
  C:0BD9H         LINE#         189
  C:0BE3H         LINE#         190
  C:0BE5H         LINE#         192
  C:0BE5H         LINE#         193
  C:0BE8H         LINE#         194
  C:0BEBH         LINE#         197
  C:0BF7H         LINE#         198
  C:0C04H         LINE#         199
  C:0C11H         LINE#         202
  C:0C11H         LINE#         204
  C:0C11H         LINE#         206
  C:0C11H         LINE#         207
  C:0C11H         LINE#         208
  -------         ENDPROC       LCD9648_INIT_PROC
  -------         PROC          LCD_INIT_TEST
  C:1255H         LINE#         210
  C:1255H         LINE#         211
  C:1255H         LINE#         212
  C:1259H         LINE#         213
  C:1259H         LINE#         214
  C:125CH         LINE#         215
  C:125EH         LINE#         216
  C:1265H         LINE#         217
  C:1265H         LINE#         218
  C:1268H         LINE#         219
  C:1268H         LINE#         220
  C:1268H         LINE#         221
  -------         ENDPROC       LCD_INIT_TEST
  -------         PROC          LCD_PROC
  C:12BCH         LINE#         223
  C:12BCH         LINE#         224
  C:12BCH         LINE#         226
  -------         ENDPROC       LCD_PROC
  -------         PROC          MAIN
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  21:47:58  PAGE 7


  C:128EH         LINE#         230
  C:128EH         LINE#         231
  C:128EH         LINE#         232
  C:1291H         LINE#         233
  C:1294H         LINE#         235
  C:1294H         LINE#         236
  C:1294H         LINE#         237
  C:1297H         LINE#         239
  C:129AH         LINE#         240
  C:129DH         LINE#         241
  -------         ENDPROC       MAIN
  -------         ENDMOD        MAIN

  -------         MODULE        DS18B20
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  C:12AEH         PUBLIC        ds18b20_reset
  D:00A8H         PUBLIC        IE
  C:0003H         PUBLIC        ds18b20_start
  D:00B8H         PUBLIC        IP
  C:11BCH         PUBLIC        ds18b20_read_byte
  C:12C9H         PUBLIC        ds18b20_init
  C:120CH         PUBLIC        ds18b20_read_bit
  C:1130H         PUBLIC        _ds18b20_write_byte
  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
  C:0FA8H         PUBLIC        ds18b20_read_temperture
  C:129FH         PUBLIC        _delay_10us
  B:00B0H.7       PUBLIC        DS18B20_PORT
  D:00C8H         PUBLIC        T2CON
  C:10EDH         PUBLIC        ds18b20_check
  D:00D0H         PUBLIC        PSW
  -------         PROC          _DELAY_10US
  D:0007H         SYMBOL        xms
  -------         DO            
  D:0006H         SYMBOL        i
  -------         ENDDO         
  C:129FH         LINE#         4
  C:129FH         LINE#         5
  C:129FH         LINE#         7
  C:12A5H         LINE#         8
  C:12A5H         LINE#         9
  C:12A7H         LINE#         10
  C:12ADH         LINE#         12
  -------         ENDPROC       _DELAY_10US
  -------         PROC          DS18B20_RESET
  C:12AEH         LINE#         20
  C:12AEH         LINE#         21
  C:12AEH         LINE#         22
  C:12B0H         LINE#         23
  C:12B5H         LINE#         24
  C:12B7H         LINE#         25
  -------         ENDPROC       DS18B20_RESET
  -------         PROC          DS18B20_CHECK
  -------         DO            
  D:0004H         SYMBOL        time_temp
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  21:47:58  PAGE 8


  -------         ENDDO         
  C:10EDH         LINE#         34
  C:10EDH         LINE#         35
  C:10EDH         LINE#         36
  C:10EFH         LINE#         38
  C:10F8H         LINE#         39
  C:10F8H         LINE#         40
  C:10F9H         LINE#         41
  C:10FEH         LINE#         42
  C:1100H         LINE#         43
  C:1109H         LINE#         44
  C:110BH         LINE#         45
  C:1114H         LINE#         46
  C:1114H         LINE#         47
  C:1115H         LINE#         48
  C:111AH         LINE#         49
  C:111CH         LINE#         50
  C:1125H         LINE#         51
  C:1127H         LINE#         52
  -------         ENDPROC       DS18B20_CHECK
  -------         PROC          DS18B20_READ_BIT
  -------         DO            
  D:0004H         SYMBOL        dat
  -------         ENDDO         
  C:120CH         LINE#         60
  C:120CH         LINE#         61
  C:120CH         LINE#         62
  C:120EH         LINE#         64
  C:1210H         LINE#         65
  C:1212H         LINE#         66
  C:1214H         LINE#         67
  C:1216H         LINE#         68
  C:121CH         LINE#         69
  C:121EH         LINE#         70
  C:1223H         LINE#         71
  C:1225H         LINE#         72
  -------         ENDPROC       DS18B20_READ_BIT
  -------         PROC          DS18B20_READ_BYTE
  -------         DO            
  D:0003H         SYMBOL        i
  D:0002H         SYMBOL        dat
  D:0007H         SYMBOL        temp
  -------         ENDDO         
  C:11BCH         LINE#         80
  C:11BCH         LINE#         81
  C:11BCH         LINE#         82
  C:11BEH         LINE#         83
  C:11BFH         LINE#         84
  C:11C0H         LINE#         86
  C:11C0H         LINE#         87
  C:11C0H         LINE#         88
  C:11C3H         LINE#         89
  C:11D0H         LINE#         90
  C:11D4H         LINE#         91
  C:11D6H         LINE#         92
  -------         ENDPROC       DS18B20_READ_BYTE
  -------         PROC          L?0035
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  21:47:58  PAGE 9


  -------         ENDPROC       L?0035
  -------         PROC          _DS18B20_WRITE_BYTE
  D:0004H         SYMBOL        dat
  -------         DO            
  D:0003H         SYMBOL        i
  D:0002H         SYMBOL        temp
  -------         ENDDO         
  C:1130H         LINE#         100
  C:1132H         LINE#         101
  C:1132H         LINE#         102
  C:1134H         LINE#         103
  C:1134H         LINE#         105
  C:1134H         LINE#         106
  C:1134H         LINE#         107
  C:1138H         LINE#         108
  C:113CH         LINE#         109
  C:113FH         LINE#         110
  C:113FH         LINE#         111
  C:1141H         LINE#         112
  C:1143H         LINE#         113
  C:1145H         LINE#         114
  C:114AH         LINE#         115
  C:114CH         LINE#         117
  C:114CH         LINE#         118
  C:114EH         LINE#         119
  C:1153H         LINE#         120
  C:1155H         LINE#         121
  C:1157H         LINE#         122
  C:1157H         LINE#         123
  C:115BH         LINE#         124
  -------         ENDPROC       _DS18B20_WRITE_BYTE
  -------         PROC          DS18B20_START
  C:0003H         LINE#         132
  C:0003H         LINE#         133
  C:0003H         LINE#         134
  C:0003H         LINE#         135
  C:0003H         LINE#         136
  C:0006H         LINE#         137
  -------         ENDPROC       DS18B20_START
  -------         PROC          DS18B20_INIT
  C:12C9H         LINE#         146
  C:12C9H         LINE#         147
  C:12C9H         LINE#         148
  C:12CCH         LINE#         149
  C:12CFH         LINE#         150
  -------         ENDPROC       DS18B20_INIT
  -------         PROC          DS18B20_READ_TEMPERTURE
  -------         DO            
  D:0018H         SYMBOL        temp
  D:0007H         SYMBOL        dath
  D:0001H         SYMBOL        datl
  D:001CH         SYMBOL        value
  -------         ENDDO         
  C:0FA8H         LINE#         158
  C:0FA8H         LINE#         159
  C:0FA8H         LINE#         161
  C:0FAAH         LINE#         162
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  21:47:58  PAGE 10


  C:0FABH         LINE#         163
  C:0FAFH         LINE#         165
  C:0FB2H         LINE#         166
  C:0FB2H         LINE#         167
  C:0FB2H         LINE#         168
  C:0FB5H         LINE#         169
  C:0FBAH         LINE#         171
  C:0FBFH         LINE#         172
  C:0FC2H         LINE#         173
  C:0FCEH         LINE#         175
  C:0FD5H         LINE#         176
  C:0FD5H         LINE#         177
  C:0FE6H         LINE#         178
  C:0FF4H         LINE#         179
  C:0FF6H         LINE#         181
  C:0FF6H         LINE#         182
  C:1010H         LINE#         183
  C:1010H         LINE#         184
  C:1018H         LINE#         185
  -------         ENDPROC       DS18B20_READ_TEMPERTURE
  -------         ENDMOD        DS18B20

  -------         MODULE        LCD9648
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0080H         PUBLIC        P0
  D:0090H         PUBLIC        P1
  D:00A0H         PUBLIC        P2
  D:00B0H         PUBLIC        P3
  C:11E8H         PUBLIC        _WriteData
  C:0E8EH         PUBLIC        _LCD9648_Write16EnCHAR1
  D:00A8H         PUBLIC        IE
  D:00B8H         PUBLIC        IP
  C:1284H         PUBLIC        _WriteComm
  B:00A0H.7       PUBLIC        RS
  C:1085H         PUBLIC        LCD9648_Init
  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
  B:0080H.0       PUBLIC        CS0
  C:000EH         PUBLIC        CN16CHARfont
  C:0250H         PUBLIC        EN16CHARfont
  B:00A0H.5       PUBLIC        SDA
  C:0D1FH         PUBLIC        _LCD9648_Write16CnCHAR
  C:0DE2H         PUBLIC        _LCD9648_Write16EnCHAR
  B:00A0H.6       PUBLIC        SCL
  D:00C8H         PUBLIC        T2CON
  C:1226H         PUBLIC        _SendDataSPI
  B:0080H.1       PUBLIC        RST
  C:118FH         PUBLIC        LCD9648_Clear
  D:00D0H         PUBLIC        PSW
  -------         PROC          _SENDDATASPI
  D:0007H         SYMBOL        dat
  -------         DO            
  D:0006H         SYMBOL        i
  -------         ENDDO         
  C:1226H         LINE#         4
  C:1226H         LINE#         5
  C:1226H         LINE#         8
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  21:47:58  PAGE 11


  C:1228H         LINE#         9
  C:1228H         LINE#         10
  C:1230H         LINE#         11
  C:1232H         LINE#         13
  C:1236H         LINE#         15
  C:1238H         LINE#         16
  C:123AH         LINE#         17
  C:123EH         LINE#         18
  -------         ENDPROC       _SENDDATASPI
  C:127EH         SYMBOL        L?0067
  C:1280H         SYMBOL        L?0068
  -------         PROC          L?0066
  -------         ENDPROC       L?0066
  C:127EH         SYMBOL        L?0067
  C:1280H         SYMBOL        L?0068
  -------         PROC          _WRITECOMM
  D:0006H         SYMBOL        i
  C:1284H         LINE#         20
  C:1284H         LINE#         21
  C:1284H         LINE#         23
  C:1286H         LINE#         24
  C:1288H         LINE#         26
  C:128BH         LINE#         28
  C:128DH         LINE#         30
  -------         ENDPROC       _WRITECOMM
  C:11DCH         SYMBOL        L?0070
  -------         PROC          L?0069
  -------         ENDPROC       L?0069
  C:11DCH         SYMBOL        L?0070
  -------         PROC          _WRITEDATA
  D:0006H         SYMBOL        i
  C:11E8H         LINE#         31
  C:11E8H         LINE#         32
  C:11E8H         LINE#         33
  C:11EAH         LINE#         34
  C:11ECH         LINE#         36
  C:11EFH         LINE#         38
  C:11F1H         LINE#         39
  -------         ENDPROC       _WRITEDATA
  -------         PROC          LCD9648_INIT
  -------         DO            
  D:0006H         SYMBOL        i
  -------         ENDDO         
  C:1085H         LINE#         41
  C:1085H         LINE#         42
  C:1085H         LINE#         46
  C:1087H         LINE#         47
  C:1095H         LINE#         49
  C:1097H         LINE#         50
  C:10A5H         LINE#         52
  C:10A7H         LINE#         53
  C:10B5H         LINE#         55
  C:10BCH         LINE#         56
  C:10C3H         LINE#         57
  C:10CAH         LINE#         58
  C:10D1H         LINE#         59
  C:10D8H         LINE#         60
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  21:47:58  PAGE 12


  C:10DFH         LINE#         61
  C:10E6H         LINE#         62
  -------         ENDPROC       LCD9648_INIT
  -------         PROC          LCD9648_CLEAR
  -------         DO            
  D:0005H         SYMBOL        i
  D:0004H         SYMBOL        j
  -------         ENDDO         
  C:118FH         LINE#         67
  C:118FH         LINE#         68
  C:118FH         LINE#         71
  C:1191H         LINE#         72
  C:1191H         LINE#         73
  C:1198H         LINE#         74
  C:119EH         LINE#         75
  C:11A5H         LINE#         76
  C:11ABH         LINE#         78
  C:11ADH         LINE#         79
  C:11ADH         LINE#         80
  C:11B3H         LINE#         81
  C:11B7H         LINE#         82
  C:11BBH         LINE#         83
  -------         ENDPROC       LCD9648_CLEAR
  -------         PROC          _LCD9648_WRITE16CNCHAR
  D:001EH         SYMBOL        x
  D:001FH         SYMBOL        y
  D:0001H         SYMBOL        cn
  -------         DO            
  D:0004H         SYMBOL        j
  D:0020H         SYMBOL        x1
  D:0021H         SYMBOL        x2
  D:0005H         SYMBOL        wordNum
  -------         ENDDO         
  C:0D1FH         LINE#         86
  C:0D23H         LINE#         87
  C:0D23H         LINE#         91
  C:0D2AH         LINE#         92
  C:0D2AH         LINE#         93
  C:0D2DH         LINE#         94
  C:0D2DH         LINE#         97
  C:0D34H         LINE#         98
  C:0D34H         LINE#         99
  C:0D37H         LINE#         100
  C:0D37H         LINE#         101
  C:0D3DH         LINE#         103
  C:0D43H         LINE#         104
  C:0D4BH         LINE#         105
  C:0D4BH         LINE#         108
  C:0D52H         LINE#         110
  C:0D59H         LINE#         111
  C:0D5FH         LINE#         113
  C:0D62H         LINE#         114
  C:0D69H         LINE#         115
  C:0D6BH         LINE#         116
  C:0D6BH         LINE#         118
  C:0D9EH         LINE#         120
  C:0D9EH         LINE#         121
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  21:47:58  PAGE 13


  C:0D9FH         LINE#         122
  C:0D9FH         LINE#         123
  C:0DA4H         LINE#         124
  C:0DA4H         LINE#         126
  C:0DABH         LINE#         129
  C:0DAEH         LINE#         130
  C:0DB5H         LINE#         131
  C:0DB5H         LINE#         132
  C:0DC5H         LINE#         133
  C:0DC9H         LINE#         134
  C:0DCFH         LINE#         135
  C:0DCFH         LINE#         136
  C:0DD5H         LINE#         137
  C:0DDCH         LINE#         139
  C:0DDFH         LINE#         140
  C:0DE1H         LINE#         141
  -------         ENDPROC       _LCD9648_WRITE16CNCHAR
  -------         PROC          _LCD9648_WRITE16ENCHAR
  D:001EH         SYMBOL        x
  D:001FH         SYMBOL        y
  D:0001H         SYMBOL        en
  -------         DO            
  D:0004H         SYMBOL        j
  D:0020H         SYMBOL        x1
  D:0021H         SYMBOL        x2
  D:0005H         SYMBOL        wordNum
  -------         ENDDO         
  C:0DE2H         LINE#         143
  C:0DE6H         LINE#         144
  C:0DE6H         LINE#         148
  C:0DEDH         LINE#         149
  C:0DEDH         LINE#         150
  C:0DF0H         LINE#         151
  C:0DF0H         LINE#         154
  C:0DF7H         LINE#         155
  C:0DF7H         LINE#         156
  C:0DFAH         LINE#         157
  C:0DFAH         LINE#         158
  C:0E00H         LINE#         160
  C:0E06H         LINE#         161
  C:0E0EH         LINE#         162
  C:0E0EH         LINE#         165
  C:0E15H         LINE#         167
  C:0E1CH         LINE#         168
  C:0E22H         LINE#         170
  C:0E27H         LINE#         171
  C:0E2EH         LINE#         172
  C:0E30H         LINE#         173
  C:0E30H         LINE#         175
  C:0E48H         LINE#         176
  C:0E48H         LINE#         177
  C:0E49H         LINE#         178
  C:0E49H         LINE#         179
  C:0E4EH         LINE#         180
  C:0E4EH         LINE#         182
  C:0E55H         LINE#         185
  C:0E5AH         LINE#         186
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  21:47:58  PAGE 14


  C:0E61H         LINE#         187
  C:0E61H         LINE#         188
  C:0E71H         LINE#         189
  C:0E75H         LINE#         190
  C:0E7BH         LINE#         191
  C:0E7BH         LINE#         192
  C:0E81H         LINE#         193
  C:0E88H         LINE#         195
  C:0E8BH         LINE#         196
  C:0E8DH         LINE#         197
  -------         ENDPROC       _LCD9648_WRITE16ENCHAR
  -------         PROC          _LCD9648_WRITE16ENCHAR1
  D:0001H         SYMBOL        x
  D:0005H         SYMBOL        y
  D:0003H         SYMBOL        en
  -------         DO            
  D:0002H         SYMBOL        j
  D:0022H         SYMBOL        x1
  D:0023H         SYMBOL        x2
  D:0004H         SYMBOL        wordNum
  -------         ENDDO         
  C:0E8EH         LINE#         198
  C:0E90H         LINE#         199
  C:0E90H         LINE#         203
  C:0E96H         LINE#         204
  C:0E96H         LINE#         205
  C:0E99H         LINE#         206
  C:0E99H         LINE#         209
  C:0E9FH         LINE#         210
  C:0E9FH         LINE#         211
  C:0EA2H         LINE#         212
  C:0EA2H         LINE#         213
  C:0EA6H         LINE#         215
  C:0EACH         LINE#         217
  C:0EACH         LINE#         220
  C:0EB3H         LINE#         222
  C:0EB9H         LINE#         223
  C:0EBEH         LINE#         225
  C:0EC3H         LINE#         226
  C:0ECAH         LINE#         227
  C:0ECCH         LINE#         228
  C:0ECCH         LINE#         230
  C:0EE0H         LINE#         231
  C:0EE0H         LINE#         232
  C:0EE1H         LINE#         233
  C:0EE1H         LINE#         234
  C:0EE6H         LINE#         235
  C:0EE6H         LINE#         237
  C:0EECH         LINE#         240
  C:0EF1H         LINE#         241
  C:0EF8H         LINE#         242
  C:0EF8H         LINE#         243
  C:0F0DH         LINE#         244
  C:0F11H         LINE#         245
  C:0F15H         LINE#         246
  C:0F15H         LINE#         247
  C:0F19H         LINE#         248
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  21:47:58  PAGE 15


  C:0F19H         LINE#         250
  C:0F19H         LINE#         251
  C:0F1BH         LINE#         252
  -------         ENDPROC       _LCD9648_WRITE16ENCHAR1
  -------         ENDMOD        LCD9648

  -------         MODULE        KEY
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  C:123FH         PUBLIC        Key_Read
  D:00A8H         PUBLIC        IE
  D:00B8H         PUBLIC        IP
  B:0090H.0       PUBLIC        k1
  B:0090H.1       PUBLIC        k2
  B:0090H.2       PUBLIC        k3
  B:0090H.3       PUBLIC        k4
  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
  D:00C8H         PUBLIC        T2CON
  D:00D0H         PUBLIC        PSW
  -------         PROC          KEY_READ
  -------         DO            
  D:0007H         SYMBOL        temp
  -------         ENDDO         
  C:123FH         LINE#         4
  C:123FH         LINE#         5
  C:123FH         LINE#         6
  C:1241H         LINE#         8
  C:1245H         LINE#         9
  C:124AH         LINE#         10
  C:124FH         LINE#         11
  C:1254H         LINE#         12
  C:1254H         LINE#         13
  -------         ENDPROC       KEY_READ
  -------         ENDMOD        KEY

  -------         MODULE        ?C?FPMUL
  C:089FH         PUBLIC        ?C?FPMUL
  -------         ENDMOD        ?C?FPMUL

  -------         MODULE        ?C?FCAST
  C:09AFH         PUBLIC        ?C?FCASTC
  C:09AAH         PUBLIC        ?C?FCASTI
  C:09A5H         PUBLIC        ?C?FCASTL
  -------         ENDMOD        ?C?FCAST

  -------         MODULE        ?C?CLDPTR
  C:0A2DH         PUBLIC        ?C?CLDPTR
  -------         ENDMOD        ?C?CLDPTR

  -------         MODULE        ?C?CLDOPTR
  C:0A46H         PUBLIC        ?C?CLDOPTR
  -------         ENDMOD        ?C?CLDOPTR

  -------         MODULE        ?C?LNEG
  C:0A73H         PUBLIC        ?C?LNEG
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  21:47:58  PAGE 16


  -------         ENDMOD        ?C?LNEG

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?DS18B20_INIT?DS18B20

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?DS18B20_READ_TEMPERTURE?DS18B20

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?_LCD9648_WRITE16ENCHAR1?LCD9648

Program Size: data=37.0 xdata=0 code=4816
LINK/LOCATE RUN COMPLETE.  3 WARNING(S),  0 ERROR(S)
