BL<PERSON> BANKED LINKER/LOCATER V6.22                                                        07/07/2025  21:13:28  PAGE 1


BL51 BANKED LINKER/LOCATER V6.22, INVOKED BY:
E:\KEIL\C51\BIN\BL51.EXE .\Objects\main.obj, .\Objects\ds18b20.obj, .\Objects\lcd9648.obj, .\Objects\Key.obj TO .\Object
>> s\Project PRINT (.\Listings\Project.m51) RAMSIZE (256)


MEMORY MODEL: SMALL WITH FLOATING POINT ARITHMETIC


INPUT MODULES INCLUDED:
  .\Objects\main.obj (MAIN)
  .\Objects\ds18b20.obj (DS18B20)
  .\Objects\lcd9648.obj (LCD9648)
  .\Objects\Key.obj (KEY)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?FPMUL)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?FCAST)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?FPGETOPN)
  E:\KEIL\C51\LIB\C51S.LIB (?C_STARTUP)
  E:\KEIL\C51\LIB\C51S.LIB (?C?CLDPTR)
  E:\KEIL\C51\LIB\C51S.LIB (?C?CLDOPTR)
  E:\KEIL\C51\LIB\C51S.LIB (?C?LNEG)
  E:\KEIL\C51\LIB\C51S.LIB (?C_INIT)


LINK MAP OF MODULE:  .\Objects\Project (MAIN)


            TYPE    BASE      LENGTH    RELOCATION   SEGMENT NAME
            -----------------------------------------------------

            * * * * * * *   D A T A   M E M O R Y   * * * * * * *
            REG     0000H     0008H     ABSOLUTE     "REG BANK 0"
            DATA    0008H     0010H     UNIT         ?DT?MAIN
            DATA    0018H     0006H     UNIT         ?DT?DS18B20_READ_TEMPERTURE?DS18B20
            DATA    001EH     0004H     UNIT         _DATA_GROUP_
            DATA    0022H     0002H     UNIT         ?DT?_LCD9648_WRITE16ENCHAR1?LCD9648
            IDATA   0024H     0001H     UNIT         ?STACK

            * * * * * * *   C O D E   M E M O R Y   * * * * * * *
            CODE    0000H     0003H     ABSOLUTE     
            CODE    0003H     0008H     UNIT         ?PR?DS18B20_START?DS18B20
            CODE    000BH     0003H     ABSOLUTE     
            CODE    000EH     097FH     UNIT         ?CO?LCD9648
            CODE    098DH     01E2H     UNIT         ?C?LIB_CODE
            CODE    0B6FH     0191H     UNIT         ?PR?LCD9648_INIT_PROC?MAIN
            CODE    0D00H     010DH     UNIT         ?PR?KEY_PROC?MAIN
            CODE    0E0DH     00C3H     UNIT         ?PR?_LCD9648_WRITE16CNCHAR?LCD9648
            CODE    0ED0H     00ACH     UNIT         ?PR?_LCD9648_WRITE16ENCHAR?LCD9648
            CODE    0F7CH     008EH     UNIT         ?PR?_LCD9648_WRITE16ENCHAR1?LCD9648
            CODE    100AH     008CH     UNIT         ?C_C51STARTUP
            CODE    1096H     0071H     UNIT         ?PR?DS18B20_READ_TEMPERTURE?DS18B20
            CODE    1107H     0068H     UNIT         ?PR?LCD9648_INIT?LCD9648
            CODE    116FH     0067H     UNIT         ?CO?MAIN
            CODE    11D6H     003BH     UNIT         ?PR?DS18B20_CHECK?DS18B20
            CODE    1211H     0034H     UNIT         ?PR?_DS18B20_WRITE_BYTE?DS18B20
            CODE    1245H     0033H     UNIT         ?PR?TIMER0_ISR?MAIN
            CODE    1278H     002DH     UNIT         ?PR?LCD9648_CLEAR?LCD9648
            CODE    12A5H     001BH     UNIT         ?PR?DS18B20_READ_BYTE?DS18B20
            CODE    12C0H     001BH     UNIT         ?PR?_WRITEDATA?LCD9648
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  21:13:28  PAGE 2


            CODE    12DBH     001AH     UNIT         ?C_INITSEG
            CODE    12F5H     001AH     UNIT         ?PR?DS18B20_READ_BIT?DS18B20
            CODE    130FH     0019H     UNIT         ?PR?_SENDDATASPI?LCD9648
            CODE    1328H     0016H     UNIT         ?PR?KEY_READ?KEY
            CODE    133EH     0014H     UNIT         ?PR?LCD_INIT_TEST?MAIN
            CODE    1352H     0013H     UNIT         ?PR?TIMER0_INIT?MAIN
            CODE    1365H     0012H     UNIT         ?PR?_WRITECOMM?LCD9648
            CODE    1377H     000FH     UNIT         ?PR?_DELAY_10US?DS18B20
            CODE    1386H     000EH     UNIT         ?PR?DS18B20_RESET?DS18B20
            CODE    1394H     000BH     UNIT         ?PR?MAIN?MAIN
            CODE    139FH     0007H     UNIT         ?PR?DS18B20_INIT?DS18B20
            CODE    13A6H     0001H     UNIT         ?PR?LCD_PROC?MAIN



OVERLAY MAP OF MODULE:   .\Objects\Project (MAIN)


SEGMENT                                       DATA_GROUP 
  +--> CALLED SEGMENT                       START    LENGTH
-----------------------------------------------------------
?C_C51STARTUP                               -----    -----
  +--> ?PR?MAIN?MAIN
  +--> ?C_INITSEG

?PR?MAIN?MAIN                               -----    -----
  +--> ?PR?LCD9648_INIT_PROC?MAIN
  +--> ?PR?TIMER0_INIT?MAIN
  +--> ?PR?LCD_INIT_TEST?MAIN

?PR?LCD9648_INIT_PROC?MAIN                  -----    -----
  +--> ?PR?LCD9648_INIT?LCD9648
  +--> ?PR?LCD9648_CLEAR?LCD9648
  +--> ?CO?MAIN
  +--> ?PR?_LCD9648_WRITE16CNCHAR?LCD9648
  +--> ?PR?_LCD9648_WRITE16ENCHAR?LCD9648

?PR?LCD9648_INIT?LCD9648                    -----    -----
  +--> ?PR?_WRITECOMM?LCD9648

?PR?_WRITECOMM?LCD9648                      -----    -----
  +--> ?PR?_SENDDATASPI?LCD9648

?PR?LCD9648_CLEAR?LCD9648                   -----    -----
  +--> ?PR?_WRITECOMM?LCD9648
  +--> ?PR?_WRITEDATA?LCD9648

?PR?_WRITEDATA?LCD9648                      -----    -----
  +--> ?PR?_SENDDATASPI?LCD9648

?PR?_LCD9648_WRITE16CNCHAR?LCD9648          001EH    0004H
  +--> ?PR?_WRITECOMM?LCD9648
  +--> ?CO?LCD9648
  +--> ?PR?_WRITEDATA?LCD9648

?PR?_LCD9648_WRITE16ENCHAR?LCD9648          001EH    0004H
  +--> ?PR?_WRITECOMM?LCD9648
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  21:13:28  PAGE 3


  +--> ?CO?LCD9648
  +--> ?PR?_WRITEDATA?LCD9648

?PR?LCD_INIT_TEST?MAIN                      -----    -----
  +--> ?PR?LCD9648_INIT_PROC?MAIN



SYMBOL TABLE OF MODULE:  .\Objects\Project (MAIN)

  VALUE           TYPE          NAME
  ----------------------------------

  -------         MODULE        MAIN
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0080H         PUBLIC        P0
  D:0090H         PUBLIC        P1
  D:00A0H         PUBLIC        P2
  D:00B0H         PUBLIC        P3
  C:1245H         PUBLIC        Timer0_ISR
  B:00A8H.7       PUBLIC        EA
  D:00A8H         PUBLIC        IE
  D:0008H         PUBLIC        Temperature
  D:00B8H         PUBLIC        IP
  C:0B6FH         PUBLIC        LCD9648_Init_Proc
  C:0D00H         PUBLIC        Key_Proc
  D:0009H         PUBLIC        Key_Down
  C:1394H         PUBLIC        main
  C:1352H         PUBLIC        Timer0_Init
  D:000AH         PUBLIC        Key_Old
  D:000BH         PUBLIC        Key_Slow_Down
  D:000CH         PUBLIC        LCD_Disp_Mode
  D:000DH         PUBLIC        Key_Val
  D:000EH         PUBLIC        LCD_Init_Mode
  D:0098H         PUBLIC        SCON
  D:0089H         PUBLIC        TMOD
  D:0088H         PUBLIC        TCON
  D:000FH         PUBLIC        Timer0_count
  B:00A8H.1       PUBLIC        ET0
  C:13A6H         PUBLIC        LCD_Proc
  D:008CH         PUBLIC        TH0
  C:133EH         PUBLIC        LCD_Init_Test
  D:0011H         PUBLIC        Key_Up
  D:008AH         PUBLIC        TL0
  B:0088H.4       PUBLIC        TR0
  D:0012H         PUBLIC        State_Index
  D:00C8H         PUBLIC        T2CON
  D:0013H         PUBLIC        Time_Flag
  D:0014H         PUBLIC        State_Disp
  D:00D0H         PUBLIC        PSW
  -------         PROC          KEY_PROC
  C:0D00H         LINE#         23
  C:0D00H         LINE#         24
  C:0D00H         LINE#         25
  C:0D07H         LINE#         26
  C:0D0AH         LINE#         28
  C:0D0FH         LINE#         29
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  21:13:28  PAGE 4


  C:0D18H         LINE#         30
  C:0D1EH         LINE#         31
  C:0D21H         LINE#         33
  C:0D3AH         LINE#         34
  C:0D3AH         LINE#         35
  C:0D3AH         LINE#         36
  C:0D43H         LINE#         37
  C:0D43H         LINE#         38
  C:0D47H         LINE#         39
  C:0D47H         LINE#         40
  C:0D51H         LINE#         41
  C:0D58H         LINE#         42
  C:0D58H         LINE#         44
  C:0D5DH         LINE#         45
  C:0D5DH         LINE#         46
  C:0D65H         LINE#         47
  C:0D6CH         LINE#         48
  C:0D6CH         LINE#         50
  C:0D71H         LINE#         51
  C:0D71H         LINE#         52
  C:0D79H         LINE#         53
  C:0D80H         LINE#         54
  C:0D80H         LINE#         56
  C:0D89H         LINE#         57
  C:0D89H         LINE#         58
  C:0D94H         LINE#         59
  C:0D94H         LINE#         60
  C:0D94H         LINE#         61
  C:0D94H         LINE#         62
  C:0D96H         LINE#         64
  C:0D96H         LINE#         65
  C:0D9CH         LINE#         66
  C:0D9CH         LINE#         67
  C:0DA0H         LINE#         68
  C:0DA0H         LINE#         69
  C:0DAAH         LINE#         70
  C:0DB1H         LINE#         71
  C:0DB1H         LINE#         73
  C:0DB6H         LINE#         74
  C:0DB6H         LINE#         75
  C:0DBEH         LINE#         76
  C:0DC5H         LINE#         77
  C:0DC5H         LINE#         79
  C:0DCAH         LINE#         80
  C:0DCAH         LINE#         81
  C:0DD2H         LINE#         82
  C:0DD9H         LINE#         83
  C:0DD9H         LINE#         85
  C:0DDFH         LINE#         86
  C:0DDFH         LINE#         87
  C:0DEAH         LINE#         88
  C:0DF1H         LINE#         89
  C:0DF1H         LINE#         90
  C:0DF1H         LINE#         91
  C:0DF2H         LINE#         93
  C:0DF2H         LINE#         94
  C:0DFCH         LINE#         95
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  21:13:28  PAGE 5


  C:0DFDH         LINE#         97
  C:0DFDH         LINE#         98
  C:0E02H         LINE#         99
  C:0E02H         LINE#         100
  C:0E09H         LINE#         101
  C:0E0CH         LINE#         102
  C:0E0CH         LINE#         103
  C:0E0CH         LINE#         104
  C:0E0CH         LINE#         105
  -------         ENDPROC       KEY_PROC
  -------         PROC          TIMER0_INIT
  C:1352H         LINE#         116
  C:1352H         LINE#         117
  C:1352H         LINE#         118
  C:1355H         LINE#         119
  C:1358H         LINE#         120
  C:135BH         LINE#         121
  C:135EH         LINE#         122
  C:1360H         LINE#         123
  C:1362H         LINE#         124
  C:1364H         LINE#         125
  -------         ENDPROC       TIMER0_INIT
  -------         PROC          TIMER0_ISR
  C:1245H         LINE#         131
  C:1249H         LINE#         133
  C:124CH         LINE#         134
  C:124FH         LINE#         136
  C:1257H         LINE#         138
  C:1260H         LINE#         139
  C:1260H         LINE#         140
  C:1263H         LINE#         141
  C:1269H         LINE#         143
  C:1269H         LINE#         145
  C:1273H         LINE#         146
  -------         ENDPROC       TIMER0_ISR
  -------         PROC          LCD9648_INIT_PROC
  C:0B6FH         LINE#         147
  C:0B6FH         LINE#         148
  C:0B6FH         LINE#         149
  C:0B85H         LINE#         150
  C:0B85H         LINE#         151
  C:0B85H         LINE#         152
  C:0B88H         LINE#         153
  C:0B8BH         LINE#         155
  C:0B97H         LINE#         156
  C:0BA4H         LINE#         157
  C:0BB1H         LINE#         159
  C:0BBEH         LINE#         161
  C:0BCBH         LINE#         162
  C:0BD8H         LINE#         163
  C:0BE5H         LINE#         164
  C:0BF2H         LINE#         165
  C:0BFFH         LINE#         166
  C:0BFFH         LINE#         168
  C:0BFFH         LINE#         169
  C:0C02H         LINE#         170
  C:0C05H         LINE#         172
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  21:13:28  PAGE 6


  C:0C11H         LINE#         173
  C:0C1EH         LINE#         174
  C:0C2BH         LINE#         175
  C:0C38H         LINE#         176
  C:0C45H         LINE#         177
  C:0C52H         LINE#         179
  C:0C5FH         LINE#         180
  C:0C6CH         LINE#         181
  C:0C79H         LINE#         182
  C:0C86H         LINE#         183
  C:0C93H         LINE#         184
  C:0CA0H         LINE#         186
  C:0CADH         LINE#         187
  C:0CBAH         LINE#         188
  C:0CC7H         LINE#         189
  C:0CD1H         LINE#         190
  C:0CD3H         LINE#         192
  C:0CD3H         LINE#         193
  C:0CD6H         LINE#         194
  C:0CD9H         LINE#         197
  C:0CE5H         LINE#         198
  C:0CF2H         LINE#         199
  C:0CFFH         LINE#         200
  C:0CFFH         LINE#         202
  C:0CFFH         LINE#         204
  C:0CFFH         LINE#         205
  C:0CFFH         LINE#         206
  -------         ENDPROC       LCD9648_INIT_PROC
  -------         PROC          LCD_INIT_TEST
  C:133EH         LINE#         208
  C:133EH         LINE#         209
  C:133EH         LINE#         210
  C:1342H         LINE#         211
  C:1342H         LINE#         212
  C:1345H         LINE#         213
  C:1347H         LINE#         214
  C:134EH         LINE#         215
  C:134EH         LINE#         216
  C:1351H         LINE#         217
  C:1351H         LINE#         218
  C:1351H         LINE#         219
  -------         ENDPROC       LCD_INIT_TEST
  -------         PROC          LCD_PROC
  C:13A6H         LINE#         221
  C:13A6H         LINE#         222
  C:13A6H         LINE#         224
  -------         ENDPROC       LCD_PROC
  -------         PROC          MAIN
  C:1394H         LINE#         227
  C:1394H         LINE#         228
  C:1394H         LINE#         229
  C:1397H         LINE#         230
  C:139AH         LINE#         232
  C:139AH         LINE#         233
  C:139AH         LINE#         234
  C:139DH         LINE#         238
  -------         ENDPROC       MAIN
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  21:13:28  PAGE 7


  -------         ENDMOD        MAIN

  -------         MODULE        DS18B20
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  C:1386H         PUBLIC        ds18b20_reset
  D:00A8H         PUBLIC        IE
  C:0003H         PUBLIC        ds18b20_start
  D:00B8H         PUBLIC        IP
  C:12A5H         PUBLIC        ds18b20_read_byte
  C:139FH         PUBLIC        ds18b20_init
  C:12F5H         PUBLIC        ds18b20_read_bit
  C:1219H         PUBLIC        _ds18b20_write_byte
  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
  C:1096H         PUBLIC        ds18b20_read_temperture
  C:1377H         PUBLIC        _delay_10us
  B:00B0H.7       PUBLIC        DS18B20_PORT
  D:00C8H         PUBLIC        T2CON
  C:11D6H         PUBLIC        ds18b20_check
  D:00D0H         PUBLIC        PSW
  -------         PROC          _DELAY_10US
  D:0007H         SYMBOL        xms
  -------         DO            
  D:0006H         SYMBOL        i
  -------         ENDDO         
  C:1377H         LINE#         4
  C:1377H         LINE#         5
  C:1377H         LINE#         7
  C:137DH         LINE#         8
  C:137DH         LINE#         9
  C:137FH         LINE#         10
  C:1385H         LINE#         12
  -------         ENDPROC       _DELAY_10US
  -------         PROC          DS18B20_RESET
  C:1386H         LINE#         20
  C:1386H         LINE#         21
  C:1386H         LINE#         22
  C:1388H         LINE#         23
  C:138DH         LINE#         24
  C:138FH         LINE#         25
  -------         ENDPROC       DS18B20_RESET
  -------         PROC          DS18B20_CHECK
  -------         DO            
  D:0004H         SYMBOL        time_temp
  -------         ENDDO         
  C:11D6H         LINE#         34
  C:11D6H         LINE#         35
  C:11D6H         LINE#         36
  C:11D8H         LINE#         38
  C:11E1H         LINE#         39
  C:11E1H         LINE#         40
  C:11E2H         LINE#         41
  C:11E7H         LINE#         42
  C:11E9H         LINE#         43
  C:11F2H         LINE#         44
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  21:13:28  PAGE 8


  C:11F4H         LINE#         45
  C:11FDH         LINE#         46
  C:11FDH         LINE#         47
  C:11FEH         LINE#         48
  C:1203H         LINE#         49
  C:1205H         LINE#         50
  C:120EH         LINE#         51
  C:1210H         LINE#         52
  -------         ENDPROC       DS18B20_CHECK
  -------         PROC          DS18B20_READ_BIT
  -------         DO            
  D:0004H         SYMBOL        dat
  -------         ENDDO         
  C:12F5H         LINE#         60
  C:12F5H         LINE#         61
  C:12F5H         LINE#         62
  C:12F7H         LINE#         64
  C:12F9H         LINE#         65
  C:12FBH         LINE#         66
  C:12FDH         LINE#         67
  C:12FFH         LINE#         68
  C:1305H         LINE#         69
  C:1307H         LINE#         70
  C:130CH         LINE#         71
  C:130EH         LINE#         72
  -------         ENDPROC       DS18B20_READ_BIT
  -------         PROC          DS18B20_READ_BYTE
  -------         DO            
  D:0003H         SYMBOL        i
  D:0002H         SYMBOL        dat
  D:0007H         SYMBOL        temp
  -------         ENDDO         
  C:12A5H         LINE#         80
  C:12A5H         LINE#         81
  C:12A5H         LINE#         82
  C:12A7H         LINE#         83
  C:12A8H         LINE#         84
  C:12A9H         LINE#         86
  C:12A9H         LINE#         87
  C:12A9H         LINE#         88
  C:12ACH         LINE#         89
  C:12B9H         LINE#         90
  C:12BDH         LINE#         91
  C:12BFH         LINE#         92
  -------         ENDPROC       DS18B20_READ_BYTE
  -------         PROC          L?0035
  -------         ENDPROC       L?0035
  -------         PROC          _DS18B20_WRITE_BYTE
  D:0004H         SYMBOL        dat
  -------         DO            
  D:0003H         SYMBOL        i
  D:0002H         SYMBOL        temp
  -------         ENDDO         
  C:1219H         LINE#         100
  C:121BH         LINE#         101
  C:121BH         LINE#         102
  C:121DH         LINE#         103
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  21:13:28  PAGE 9


  C:121DH         LINE#         105
  C:121DH         LINE#         106
  C:121DH         LINE#         107
  C:1221H         LINE#         108
  C:1225H         LINE#         109
  C:1228H         LINE#         110
  C:1228H         LINE#         111
  C:122AH         LINE#         112
  C:122CH         LINE#         113
  C:122EH         LINE#         114
  C:1233H         LINE#         115
  C:1235H         LINE#         117
  C:1235H         LINE#         118
  C:1237H         LINE#         119
  C:123CH         LINE#         120
  C:123EH         LINE#         121
  C:1240H         LINE#         122
  C:1240H         LINE#         123
  C:1244H         LINE#         124
  -------         ENDPROC       _DS18B20_WRITE_BYTE
  -------         PROC          DS18B20_START
  C:0003H         LINE#         132
  C:0003H         LINE#         133
  C:0003H         LINE#         134
  C:0003H         LINE#         135
  C:0003H         LINE#         136
  C:0006H         LINE#         137
  -------         ENDPROC       DS18B20_START
  -------         PROC          DS18B20_INIT
  C:139FH         LINE#         146
  C:139FH         LINE#         147
  C:139FH         LINE#         148
  C:13A2H         LINE#         149
  C:13A5H         LINE#         150
  -------         ENDPROC       DS18B20_INIT
  -------         PROC          DS18B20_READ_TEMPERTURE
  -------         DO            
  D:0018H         SYMBOL        temp
  D:0007H         SYMBOL        dath
  D:0001H         SYMBOL        datl
  D:001CH         SYMBOL        value
  -------         ENDDO         
  C:1096H         LINE#         158
  C:1096H         LINE#         159
  C:1096H         LINE#         161
  C:1098H         LINE#         162
  C:1099H         LINE#         163
  C:109DH         LINE#         165
  C:10A0H         LINE#         166
  C:10A0H         LINE#         167
  C:10A0H         LINE#         168
  C:10A3H         LINE#         169
  C:10A8H         LINE#         171
  C:10ADH         LINE#         172
  C:10B0H         LINE#         173
  C:10BCH         LINE#         175
  C:10C3H         LINE#         176
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  21:13:28  PAGE 10


  C:10C3H         LINE#         177
  C:10D4H         LINE#         178
  C:10E2H         LINE#         179
  C:10E4H         LINE#         181
  C:10E4H         LINE#         182
  C:10FEH         LINE#         183
  C:10FEH         LINE#         184
  C:1106H         LINE#         185
  -------         ENDPROC       DS18B20_READ_TEMPERTURE
  -------         ENDMOD        DS18B20

  -------         MODULE        LCD9648
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0080H         PUBLIC        P0
  D:0090H         PUBLIC        P1
  D:00A0H         PUBLIC        P2
  D:00B0H         PUBLIC        P3
  C:12D1H         PUBLIC        _WriteData
  C:0F7CH         PUBLIC        _LCD9648_Write16EnCHAR1
  D:00A8H         PUBLIC        IE
  D:00B8H         PUBLIC        IP
  C:136DH         PUBLIC        _WriteComm
  B:00A0H.7       PUBLIC        RS
  C:1107H         PUBLIC        LCD9648_Init
  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
  B:0080H.0       PUBLIC        CS0
  C:000EH         PUBLIC        CN16CHARfont
  C:033EH         PUBLIC        EN16CHARfont
  B:00A0H.5       PUBLIC        SDA
  C:0E0DH         PUBLIC        _LCD9648_Write16CnCHAR
  C:0ED0H         PUBLIC        _LCD9648_Write16EnCHAR
  B:00A0H.6       PUBLIC        SCL
  D:00C8H         PUBLIC        T2CON
  C:130FH         PUBLIC        _SendDataSPI
  B:0080H.1       PUBLIC        RST
  C:1278H         PUBLIC        LCD9648_Clear
  D:00D0H         PUBLIC        PSW
  -------         PROC          _SENDDATASPI
  D:0007H         SYMBOL        dat
  -------         DO            
  D:0006H         SYMBOL        i
  -------         ENDDO         
  C:130FH         LINE#         4
  C:130FH         LINE#         5
  C:130FH         LINE#         8
  C:1311H         LINE#         9
  C:1311H         LINE#         10
  C:1319H         LINE#         11
  C:131BH         LINE#         13
  C:131FH         LINE#         15
  C:1321H         LINE#         16
  C:1323H         LINE#         17
  C:1327H         LINE#         18
  -------         ENDPROC       _SENDDATASPI
  C:1367H         SYMBOL        L?0067
  C:1369H         SYMBOL        L?0068
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  21:13:28  PAGE 11


  -------         PROC          L?0066
  -------         ENDPROC       L?0066
  C:1367H         SYMBOL        L?0067
  C:1369H         SYMBOL        L?0068
  -------         PROC          _WRITECOMM
  D:0006H         SYMBOL        i
  C:136DH         LINE#         20
  C:136DH         LINE#         21
  C:136DH         LINE#         23
  C:136FH         LINE#         24
  C:1371H         LINE#         26
  C:1374H         LINE#         28
  C:1376H         LINE#         30
  -------         ENDPROC       _WRITECOMM
  C:12C5H         SYMBOL        L?0070
  -------         PROC          L?0069
  -------         ENDPROC       L?0069
  C:12C5H         SYMBOL        L?0070
  -------         PROC          _WRITEDATA
  D:0006H         SYMBOL        i
  C:12D1H         LINE#         31
  C:12D1H         LINE#         32
  C:12D1H         LINE#         33
  C:12D3H         LINE#         34
  C:12D5H         LINE#         36
  C:12D8H         LINE#         38
  C:12DAH         LINE#         39
  -------         ENDPROC       _WRITEDATA
  -------         PROC          LCD9648_INIT
  -------         DO            
  D:0006H         SYMBOL        i
  -------         ENDDO         
  C:1107H         LINE#         41
  C:1107H         LINE#         42
  C:1107H         LINE#         46
  C:1109H         LINE#         47
  C:1117H         LINE#         49
  C:1119H         LINE#         50
  C:1127H         LINE#         52
  C:1129H         LINE#         53
  C:1137H         LINE#         55
  C:113EH         LINE#         56
  C:1145H         LINE#         57
  C:114CH         LINE#         58
  C:1153H         LINE#         59
  C:115AH         LINE#         60
  C:1161H         LINE#         61
  C:1168H         LINE#         62
  -------         ENDPROC       LCD9648_INIT
  -------         PROC          LCD9648_CLEAR
  -------         DO            
  D:0005H         SYMBOL        i
  D:0004H         SYMBOL        j
  -------         ENDDO         
  C:1278H         LINE#         67
  C:1278H         LINE#         68
  C:1278H         LINE#         71
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  21:13:28  PAGE 12


  C:127AH         LINE#         72
  C:127AH         LINE#         73
  C:1281H         LINE#         74
  C:1287H         LINE#         75
  C:128EH         LINE#         76
  C:1294H         LINE#         78
  C:1296H         LINE#         79
  C:1296H         LINE#         80
  C:129CH         LINE#         81
  C:12A0H         LINE#         82
  C:12A4H         LINE#         83
  -------         ENDPROC       LCD9648_CLEAR
  -------         PROC          _LCD9648_WRITE16CNCHAR
  D:001EH         SYMBOL        x
  D:001FH         SYMBOL        y
  D:0001H         SYMBOL        cn
  -------         DO            
  D:0004H         SYMBOL        j
  D:0020H         SYMBOL        x1
  D:0021H         SYMBOL        x2
  D:0005H         SYMBOL        wordNum
  -------         ENDDO         
  C:0E0DH         LINE#         86
  C:0E11H         LINE#         87
  C:0E11H         LINE#         91
  C:0E18H         LINE#         92
  C:0E18H         LINE#         93
  C:0E1BH         LINE#         94
  C:0E1BH         LINE#         97
  C:0E22H         LINE#         98
  C:0E22H         LINE#         99
  C:0E25H         LINE#         100
  C:0E25H         LINE#         101
  C:0E2BH         LINE#         103
  C:0E31H         LINE#         104
  C:0E39H         LINE#         105
  C:0E39H         LINE#         108
  C:0E40H         LINE#         110
  C:0E47H         LINE#         111
  C:0E4DH         LINE#         113
  C:0E50H         LINE#         114
  C:0E57H         LINE#         115
  C:0E59H         LINE#         116
  C:0E59H         LINE#         118
  C:0E8CH         LINE#         120
  C:0E8CH         LINE#         121
  C:0E8DH         LINE#         122
  C:0E8DH         LINE#         123
  C:0E92H         LINE#         124
  C:0E92H         LINE#         126
  C:0E99H         LINE#         129
  C:0E9CH         LINE#         130
  C:0EA3H         LINE#         131
  C:0EA3H         LINE#         132
  C:0EB3H         LINE#         133
  C:0EB7H         LINE#         134
  C:0EBDH         LINE#         135
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  21:13:28  PAGE 13


  C:0EBDH         LINE#         136
  C:0EC3H         LINE#         137
  C:0ECAH         LINE#         139
  C:0ECDH         LINE#         140
  C:0ECFH         LINE#         141
  -------         ENDPROC       _LCD9648_WRITE16CNCHAR
  -------         PROC          _LCD9648_WRITE16ENCHAR
  D:001EH         SYMBOL        x
  D:001FH         SYMBOL        y
  D:0001H         SYMBOL        en
  -------         DO            
  D:0004H         SYMBOL        j
  D:0020H         SYMBOL        x1
  D:0021H         SYMBOL        x2
  D:0005H         SYMBOL        wordNum
  -------         ENDDO         
  C:0ED0H         LINE#         143
  C:0ED4H         LINE#         144
  C:0ED4H         LINE#         148
  C:0EDBH         LINE#         149
  C:0EDBH         LINE#         150
  C:0EDEH         LINE#         151
  C:0EDEH         LINE#         154
  C:0EE5H         LINE#         155
  C:0EE5H         LINE#         156
  C:0EE8H         LINE#         157
  C:0EE8H         LINE#         158
  C:0EEEH         LINE#         160
  C:0EF4H         LINE#         161
  C:0EFCH         LINE#         162
  C:0EFCH         LINE#         165
  C:0F03H         LINE#         167
  C:0F0AH         LINE#         168
  C:0F10H         LINE#         170
  C:0F15H         LINE#         171
  C:0F1CH         LINE#         172
  C:0F1EH         LINE#         173
  C:0F1EH         LINE#         175
  C:0F36H         LINE#         176
  C:0F36H         LINE#         177
  C:0F37H         LINE#         178
  C:0F37H         LINE#         179
  C:0F3CH         LINE#         180
  C:0F3CH         LINE#         182
  C:0F43H         LINE#         185
  C:0F48H         LINE#         186
  C:0F4FH         LINE#         187
  C:0F4FH         LINE#         188
  C:0F5FH         LINE#         189
  C:0F63H         LINE#         190
  C:0F69H         LINE#         191
  C:0F69H         LINE#         192
  C:0F6FH         LINE#         193
  C:0F76H         LINE#         195
  C:0F79H         LINE#         196
  C:0F7BH         LINE#         197
  -------         ENDPROC       _LCD9648_WRITE16ENCHAR
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  21:13:28  PAGE 14


  -------         PROC          _LCD9648_WRITE16ENCHAR1
  D:0001H         SYMBOL        x
  D:0005H         SYMBOL        y
  D:0003H         SYMBOL        en
  -------         DO            
  D:0002H         SYMBOL        j
  D:0022H         SYMBOL        x1
  D:0023H         SYMBOL        x2
  D:0004H         SYMBOL        wordNum
  -------         ENDDO         
  C:0F7CH         LINE#         198
  C:0F7EH         LINE#         199
  C:0F7EH         LINE#         203
  C:0F84H         LINE#         204
  C:0F84H         LINE#         205
  C:0F87H         LINE#         206
  C:0F87H         LINE#         209
  C:0F8DH         LINE#         210
  C:0F8DH         LINE#         211
  C:0F90H         LINE#         212
  C:0F90H         LINE#         213
  C:0F94H         LINE#         215
  C:0F9AH         LINE#         217
  C:0F9AH         LINE#         220
  C:0FA1H         LINE#         222
  C:0FA7H         LINE#         223
  C:0FACH         LINE#         225
  C:0FB1H         LINE#         226
  C:0FB8H         LINE#         227
  C:0FBAH         LINE#         228
  C:0FBAH         LINE#         230
  C:0FCEH         LINE#         231
  C:0FCEH         LINE#         232
  C:0FCFH         LINE#         233
  C:0FCFH         LINE#         234
  C:0FD4H         LINE#         235
  C:0FD4H         LINE#         237
  C:0FDAH         LINE#         240
  C:0FDFH         LINE#         241
  C:0FE6H         LINE#         242
  C:0FE6H         LINE#         243
  C:0FFBH         LINE#         244
  C:0FFFH         LINE#         245
  C:1003H         LINE#         246
  C:1003H         LINE#         247
  C:1007H         LINE#         248
  C:1007H         LINE#         250
  C:1007H         LINE#         251
  C:1009H         LINE#         252
  -------         ENDPROC       _LCD9648_WRITE16ENCHAR1
  -------         ENDMOD        LCD9648

  -------         MODULE        KEY
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  C:1328H         PUBLIC        Key_Read
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  21:13:28  PAGE 15


  D:00A8H         PUBLIC        IE
  D:00B8H         PUBLIC        IP
  B:0090H.0       PUBLIC        k1
  B:0090H.1       PUBLIC        k2
  B:0090H.2       PUBLIC        k3
  B:0090H.3       PUBLIC        k4
  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
  D:00C8H         PUBLIC        T2CON
  D:00D0H         PUBLIC        PSW
  -------         PROC          KEY_READ
  -------         DO            
  D:0007H         SYMBOL        temp
  -------         ENDDO         
  C:1328H         LINE#         4
  C:1328H         LINE#         5
  C:1328H         LINE#         6
  C:132AH         LINE#         8
  C:132EH         LINE#         9
  C:1333H         LINE#         10
  C:1338H         LINE#         11
  C:133DH         LINE#         12
  C:133DH         LINE#         13
  -------         ENDPROC       KEY_READ
  -------         ENDMOD        KEY

  -------         MODULE        ?C?FPMUL
  C:098DH         PUBLIC        ?C?FPMUL
  -------         ENDMOD        ?C?FPMUL

  -------         MODULE        ?C?FCAST
  C:0A9DH         PUBLIC        ?C?FCASTC
  C:0A98H         PUBLIC        ?C?FCASTI
  C:0A93H         PUBLIC        ?C?FCASTL
  -------         ENDMOD        ?C?FCAST

  -------         MODULE        ?C?CLDPTR
  C:0B1BH         PUBLIC        ?C?CLDPTR
  -------         ENDMOD        ?C?CLDPTR

  -------         MODULE        ?C?CLDOPTR
  C:0B34H         PUBLIC        ?C?CLDOPTR
  -------         ENDMOD        ?C?CLDOPTR

  -------         MODULE        ?C?LNEG
  C:0B61H         PUBLIC        ?C?LNEG
  -------         ENDMOD        ?C?LNEG

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?KEY_PROC?MAIN

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?LCD_PROC?MAIN

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?DS18B20_INIT?DS18B20

BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  21:13:28  PAGE 16


*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?DS18B20_READ_TEMPERTURE?DS18B20

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?_LCD9648_WRITE16ENCHAR1?LCD9648

Program Size: data=37.0 xdata=0 code=5031
LINK/LOCATE RUN COMPLETE.  5 WARNING(S),  0 ERROR(S)
