BL<PERSON> BANKED LINKER/LOCATER V6.22                                                        07/07/2025  17:50:05  PAGE 1


BL51 BANKED LINKER/LOCATER V6.22, INVOKED BY:
E:\KEIL\C51\BIN\BL51.EXE .\Objects\main.obj, .\Objects\ds18b20.obj, .\Objects\lcd9648.obj TO .\Objects\Project PRINT (.\
>> Listings\Project.m51) RAMSIZE (256)


MEMORY MODEL: SMALL WITH FLOATING POINT ARITHMETIC


INPUT MODULES INCLUDED:
  .\Objects\main.obj (MAIN)
  .\Objects\ds18b20.obj (DS18B20)
  .\Objects\lcd9648.obj (LCD9648)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?FPMUL)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?FCAST)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?FPGETOPN)
  E:\KEIL\C51\LIB\C51S.LIB (?C_STARTUP)
  E:\KEIL\C51\LIB\C51S.LIB (?C?CLDPTR)
  E:\KEIL\C51\LIB\C51S.LIB (?C?CLDOPTR)
  E:\KEIL\C51\LIB\C51S.LIB (?C?LNEG)
  E:\KEIL\C51\LIB\C51S.LIB (?C_INIT)


LINK MAP OF MODULE:  .\Objects\Project (MAIN)


            TYPE    BASE      LENGTH    RELOCATION   SEGMENT NAME
            -----------------------------------------------------

            * * * * * * *   D A T A   M E M O R Y   * * * * * * *
            REG     0000H     0008H     ABSOLUTE     "REG BANK 0"
            DATA    0008H     0006H     UNIT         ?DT?DS18B20_READ_TEMPERTURE?DS18B20
            DATA    000EH     0004H     UNIT         ?DT?MAIN
            DATA    0012H     0004H     UNIT         _DATA_GROUP_
            DATA    0016H     0002H     UNIT         ?DT?_LCD9648_WRITE16ENCHAR1?LCD9648
            IDATA   0018H     0001H     UNIT         ?STACK

            * * * * * * *   C O D E   M E M O R Y   * * * * * * *
            CODE    0000H     0003H     ABSOLUTE     
            CODE    0003H     0008H     UNIT         ?PR?DS18B20_START?DS18B20
            CODE    000BH     0003H     ABSOLUTE     
            CODE    000EH     097FH     UNIT         ?CO?LCD9648
            CODE    098DH     01E2H     UNIT         ?C?LIB_CODE
            CODE    0B6FH     0191H     UNIT         ?PR?LCD9648_INIT_PROC?MAIN
            CODE    0D00H     00C3H     UNIT         ?PR?_LCD9648_WRITE16CNCHAR?LCD9648
            CODE    0DC3H     00ACH     UNIT         ?PR?_LCD9648_WRITE16ENCHAR?LCD9648
            CODE    0E6FH     008EH     UNIT         ?PR?_LCD9648_WRITE16ENCHAR1?LCD9648
            CODE    0EFDH     008CH     UNIT         ?C_C51STARTUP
            CODE    0F89H     0071H     UNIT         ?PR?DS18B20_READ_TEMPERTURE?DS18B20
            CODE    0FFAH     0068H     UNIT         ?PR?LCD9648_INIT?LCD9648
            CODE    1062H     0067H     UNIT         ?CO?MAIN
            CODE    10C9H     003BH     UNIT         ?PR?DS18B20_CHECK?DS18B20
            CODE    1104H     0034H     UNIT         ?PR?_DS18B20_WRITE_BYTE?DS18B20
            CODE    1138H     002DH     UNIT         ?PR?LCD9648_CLEAR?LCD9648
            CODE    1165H     0029H     UNIT         ?PR?TIMER0_ISR?MAIN
            CODE    118EH     001BH     UNIT         ?PR?MAIN?MAIN
            CODE    11A9H     001BH     UNIT         ?PR?DS18B20_READ_BYTE?DS18B20
            CODE    11C4H     001BH     UNIT         ?PR?_WRITEDATA?LCD9648
            CODE    11DFH     001AH     UNIT         ?PR?DS18B20_READ_BIT?DS18B20
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  17:50:05  PAGE 2


            CODE    11F9H     0019H     UNIT         ?PR?_SENDDATASPI?LCD9648
            CODE    1212H     0013H     UNIT         ?PR?TIMER0_INIT?MAIN
            CODE    1225H     0012H     UNIT         ?PR?_WRITECOMM?LCD9648
            CODE    1237H     000FH     UNIT         ?PR?_DELAY_10US?DS18B20
            CODE    1246H     000EH     UNIT         ?PR?DS18B20_RESET?DS18B20
            CODE    1254H     000BH     UNIT         ?C_INITSEG
            CODE    125FH     0007H     UNIT         ?PR?DS18B20_INIT?DS18B20



OVERLAY MAP OF MODULE:   .\Objects\Project (MAIN)


SEGMENT                                       DATA_GROUP 
  +--> CALLED SEGMENT                       START    LENGTH
-----------------------------------------------------------
?C_C51STARTUP                               -----    -----
  +--> ?PR?MAIN?MAIN
  +--> ?C_INITSEG

?PR?MAIN?MAIN                               -----    -----
  +--> ?PR?LCD9648_INIT_PROC?MAIN
  +--> ?PR?TIMER0_INIT?MAIN

?PR?LCD9648_INIT_PROC?MAIN                  -----    -----
  +--> ?PR?LCD9648_INIT?LCD9648
  +--> ?PR?LCD9648_CLEAR?LCD9648
  +--> ?CO?MAIN
  +--> ?PR?_LCD9648_WRITE16CNCHAR?LCD9648
  +--> ?PR?_LCD9648_WRITE16ENCHAR?LCD9648

?PR?LCD9648_INIT?LCD9648                    -----    -----
  +--> ?PR?_WRITECOMM?LCD9648

?PR?_WRITECOMM?LCD9648                      -----    -----
  +--> ?PR?_SENDDATASPI?LCD9648

?PR?LCD9648_CLEAR?LCD9648                   -----    -----
  +--> ?PR?_WRITECOMM?LCD9648
  +--> ?PR?_WRITEDATA?LCD9648

?PR?_WRITEDATA?LCD9648                      -----    -----
  +--> ?PR?_SENDDATASPI?LCD9648

?PR?_LCD9648_WRITE16CNCHAR?LCD9648          0012H    0004H
  +--> ?PR?_WRITECOMM?LCD9648
  +--> ?CO?LCD9648
  +--> ?PR?_WRITEDATA?LCD9648

?PR?_LCD9648_WRITE16ENCHAR?LCD9648          0012H    0004H
  +--> ?PR?_WRITECOMM?LCD9648
  +--> ?CO?LCD9648
  +--> ?PR?_WRITEDATA?LCD9648



SYMBOL TABLE OF MODULE:  .\Objects\Project (MAIN)
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  17:50:05  PAGE 3



  VALUE           TYPE          NAME
  ----------------------------------

  -------         MODULE        MAIN
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0080H         PUBLIC        P0
  D:0090H         PUBLIC        P1
  D:00A0H         PUBLIC        P2
  D:00B0H         PUBLIC        P3
  C:1165H         PUBLIC        Timer0_ISR
  B:00A8H.7       PUBLIC        EA
  D:00A8H         PUBLIC        IE
  D:00B8H         PUBLIC        IP
  C:0B6FH         PUBLIC        LCD9648_Init_Proc
  C:118EH         PUBLIC        main
  C:1212H         PUBLIC        Timer0_Init
  D:000EH         PUBLIC        LCD_Init_Mode
  D:0098H         PUBLIC        SCON
  D:0089H         PUBLIC        TMOD
  D:0088H         PUBLIC        TCON
  D:000FH         PUBLIC        Timer0_count
  B:00A8H.1       PUBLIC        ET0
  D:008CH         PUBLIC        TH0
  D:008AH         PUBLIC        TL0
  B:0088H.4       PUBLIC        TR0
  D:00C8H         PUBLIC        T2CON
  D:0011H         PUBLIC        Time_Flag
  D:00D0H         PUBLIC        PSW
  -------         PROC          TIMER0_INIT
  C:1212H         LINE#         14
  C:1212H         LINE#         15
  C:1212H         LINE#         16
  C:1215H         LINE#         17
  C:1218H         LINE#         18
  C:121BH         LINE#         19
  C:121EH         LINE#         20
  C:1220H         LINE#         21
  C:1222H         LINE#         22
  C:1224H         LINE#         23
  -------         ENDPROC       TIMER0_INIT
  -------         PROC          TIMER0_ISR
  C:1165H         LINE#         29
  C:1169H         LINE#         31
  C:116CH         LINE#         32
  C:116FH         LINE#         34
  C:1177H         LINE#         36
  C:1180H         LINE#         37
  C:1180H         LINE#         38
  C:1183H         LINE#         39
  C:1189H         LINE#         41
  C:1189H         LINE#         42
  -------         ENDPROC       TIMER0_ISR
  -------         PROC          LCD9648_INIT_PROC
  C:0B6FH         LINE#         43
  C:0B6FH         LINE#         44
  C:0B6FH         LINE#         45
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  17:50:05  PAGE 4


  C:0B85H         LINE#         46
  C:0B85H         LINE#         47
  C:0B85H         LINE#         48
  C:0B88H         LINE#         49
  C:0B8BH         LINE#         51
  C:0B97H         LINE#         52
  C:0BA4H         LINE#         53
  C:0BB1H         LINE#         55
  C:0BBEH         LINE#         57
  C:0BCBH         LINE#         58
  C:0BD8H         LINE#         59
  C:0BE5H         LINE#         60
  C:0BF2H         LINE#         61
  C:0BFFH         LINE#         62
  C:0BFFH         LINE#         64
  C:0BFFH         LINE#         65
  C:0C02H         LINE#         66
  C:0C05H         LINE#         68
  C:0C11H         LINE#         69
  C:0C1EH         LINE#         70
  C:0C2BH         LINE#         71
  C:0C38H         LINE#         72
  C:0C45H         LINE#         73
  C:0C52H         LINE#         75
  C:0C5FH         LINE#         76
  C:0C6CH         LINE#         77
  C:0C79H         LINE#         78
  C:0C86H         LINE#         79
  C:0C93H         LINE#         80
  C:0CA0H         LINE#         82
  C:0CADH         LINE#         83
  C:0CBAH         LINE#         84
  C:0CC7H         LINE#         85
  C:0CD1H         LINE#         86
  C:0CD3H         LINE#         88
  C:0CD3H         LINE#         89
  C:0CD6H         LINE#         90
  C:0CD9H         LINE#         93
  C:0CE5H         LINE#         94
  C:0CF2H         LINE#         95
  C:0CFFH         LINE#         96
  C:0CFFH         LINE#         98
  C:0CFFH         LINE#         100
  C:0CFFH         LINE#         101
  C:0CFFH         LINE#         105
  -------         ENDPROC       LCD9648_INIT_PROC
  -------         PROC          MAIN
  C:118EH         LINE#         107
  C:118EH         LINE#         108
  C:118EH         LINE#         109
  C:1191H         LINE#         110
  C:1194H         LINE#         112
  C:1194H         LINE#         113
  C:1194H         LINE#         114
  C:1198H         LINE#         115
  C:1198H         LINE#         116
  C:119BH         LINE#         117
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  17:50:05  PAGE 5


  C:119DH         LINE#         118
  C:11A4H         LINE#         119
  C:11A4H         LINE#         120
  C:11A7H         LINE#         121
  C:11A7H         LINE#         122
  C:11A7H         LINE#         123
  -------         ENDPROC       MAIN
  -------         ENDMOD        MAIN

  -------         MODULE        DS18B20
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  C:1246H         PUBLIC        ds18b20_reset
  D:00A8H         PUBLIC        IE
  C:0003H         PUBLIC        ds18b20_start
  D:00B8H         PUBLIC        IP
  C:11A9H         PUBLIC        ds18b20_read_byte
  C:125FH         PUBLIC        ds18b20_init
  C:11DFH         PUBLIC        ds18b20_read_bit
  C:110CH         PUBLIC        _ds18b20_write_byte
  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
  C:0F89H         PUBLIC        ds18b20_read_temperture
  C:1237H         PUBLIC        _delay_10us
  B:00B0H.7       PUBLIC        DS18B20_PORT
  D:00C8H         PUBLIC        T2CON
  C:10C9H         PUBLIC        ds18b20_check
  D:00D0H         PUBLIC        PSW
  -------         PROC          _DELAY_10US
  D:0007H         SYMBOL        xms
  -------         DO            
  D:0006H         SYMBOL        i
  -------         ENDDO         
  C:1237H         LINE#         4
  C:1237H         LINE#         5
  C:1237H         LINE#         7
  C:123DH         LINE#         8
  C:123DH         LINE#         9
  C:123FH         LINE#         10
  C:1245H         LINE#         12
  -------         ENDPROC       _DELAY_10US
  -------         PROC          DS18B20_RESET
  C:1246H         LINE#         20
  C:1246H         LINE#         21
  C:1246H         LINE#         22
  C:1248H         LINE#         23
  C:124DH         LINE#         24
  C:124FH         LINE#         25
  -------         ENDPROC       DS18B20_RESET
  -------         PROC          DS18B20_CHECK
  -------         DO            
  D:0004H         SYMBOL        time_temp
  -------         ENDDO         
  C:10C9H         LINE#         34
  C:10C9H         LINE#         35
  C:10C9H         LINE#         36
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  17:50:05  PAGE 6


  C:10CBH         LINE#         38
  C:10D4H         LINE#         39
  C:10D4H         LINE#         40
  C:10D5H         LINE#         41
  C:10DAH         LINE#         42
  C:10DCH         LINE#         43
  C:10E5H         LINE#         44
  C:10E7H         LINE#         45
  C:10F0H         LINE#         46
  C:10F0H         LINE#         47
  C:10F1H         LINE#         48
  C:10F6H         LINE#         49
  C:10F8H         LINE#         50
  C:1101H         LINE#         51
  C:1103H         LINE#         52
  -------         ENDPROC       DS18B20_CHECK
  -------         PROC          DS18B20_READ_BIT
  -------         DO            
  D:0004H         SYMBOL        dat
  -------         ENDDO         
  C:11DFH         LINE#         60
  C:11DFH         LINE#         61
  C:11DFH         LINE#         62
  C:11E1H         LINE#         64
  C:11E3H         LINE#         65
  C:11E5H         LINE#         66
  C:11E7H         LINE#         67
  C:11E9H         LINE#         68
  C:11EFH         LINE#         69
  C:11F1H         LINE#         70
  C:11F6H         LINE#         71
  C:11F8H         LINE#         72
  -------         ENDPROC       DS18B20_READ_BIT
  -------         PROC          DS18B20_READ_BYTE
  -------         DO            
  D:0003H         SYMBOL        i
  D:0002H         SYMBOL        dat
  D:0007H         SYMBOL        temp
  -------         ENDDO         
  C:11A9H         LINE#         80
  C:11A9H         LINE#         81
  C:11A9H         LINE#         82
  C:11ABH         LINE#         83
  C:11ACH         LINE#         84
  C:11ADH         LINE#         86
  C:11ADH         LINE#         87
  C:11ADH         LINE#         88
  C:11B0H         LINE#         89
  C:11BDH         LINE#         90
  C:11C1H         LINE#         91
  C:11C3H         LINE#         92
  -------         ENDPROC       DS18B20_READ_BYTE
  -------         PROC          L?0035
  -------         ENDPROC       L?0035
  -------         PROC          _DS18B20_WRITE_BYTE
  D:0004H         SYMBOL        dat
  -------         DO            
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  17:50:05  PAGE 7


  D:0003H         SYMBOL        i
  D:0002H         SYMBOL        temp
  -------         ENDDO         
  C:110CH         LINE#         100
  C:110EH         LINE#         101
  C:110EH         LINE#         102
  C:1110H         LINE#         103
  C:1110H         LINE#         105
  C:1110H         LINE#         106
  C:1110H         LINE#         107
  C:1114H         LINE#         108
  C:1118H         LINE#         109
  C:111BH         LINE#         110
  C:111BH         LINE#         111
  C:111DH         LINE#         112
  C:111FH         LINE#         113
  C:1121H         LINE#         114
  C:1126H         LINE#         115
  C:1128H         LINE#         117
  C:1128H         LINE#         118
  C:112AH         LINE#         119
  C:112FH         LINE#         120
  C:1131H         LINE#         121
  C:1133H         LINE#         122
  C:1133H         LINE#         123
  C:1137H         LINE#         124
  -------         ENDPROC       _DS18B20_WRITE_BYTE
  -------         PROC          DS18B20_START
  C:0003H         LINE#         132
  C:0003H         LINE#         133
  C:0003H         LINE#         134
  C:0003H         LINE#         135
  C:0003H         LINE#         136
  C:0006H         LINE#         137
  -------         ENDPROC       DS18B20_START
  -------         PROC          DS18B20_INIT
  C:125FH         LINE#         146
  C:125FH         LINE#         147
  C:125FH         LINE#         148
  C:1262H         LINE#         149
  C:1265H         LINE#         150
  -------         ENDPROC       DS18B20_INIT
  -------         PROC          DS18B20_READ_TEMPERTURE
  -------         DO            
  D:0008H         SYMBOL        temp
  D:0007H         SYMBOL        dath
  D:0001H         SYMBOL        datl
  D:000CH         SYMBOL        value
  -------         ENDDO         
  C:0F89H         LINE#         158
  C:0F89H         LINE#         159
  C:0F89H         LINE#         161
  C:0F8BH         LINE#         162
  C:0F8CH         LINE#         163
  C:0F90H         LINE#         165
  C:0F93H         LINE#         166
  C:0F93H         LINE#         167
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  17:50:05  PAGE 8


  C:0F93H         LINE#         168
  C:0F96H         LINE#         169
  C:0F9BH         LINE#         171
  C:0FA0H         LINE#         172
  C:0FA3H         LINE#         173
  C:0FAFH         LINE#         175
  C:0FB6H         LINE#         176
  C:0FB6H         LINE#         177
  C:0FC7H         LINE#         178
  C:0FD5H         LINE#         179
  C:0FD7H         LINE#         181
  C:0FD7H         LINE#         182
  C:0FF1H         LINE#         183
  C:0FF1H         LINE#         184
  C:0FF9H         LINE#         185
  -------         ENDPROC       DS18B20_READ_TEMPERTURE
  -------         ENDMOD        DS18B20

  -------         MODULE        LCD9648
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0080H         PUBLIC        P0
  D:0090H         PUBLIC        P1
  D:00A0H         PUBLIC        P2
  D:00B0H         PUBLIC        P3
  C:11D5H         PUBLIC        _WriteData
  C:0E6FH         PUBLIC        _LCD9648_Write16EnCHAR1
  D:00A8H         PUBLIC        IE
  D:00B8H         PUBLIC        IP
  C:122DH         PUBLIC        _WriteComm
  B:00A0H.7       PUBLIC        RS
  C:0FFAH         PUBLIC        LCD9648_Init
  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
  B:0080H.0       PUBLIC        CS0
  C:000EH         PUBLIC        CN16CHARfont
  C:033EH         PUBLIC        EN16CHARfont
  B:00A0H.5       PUBLIC        SDA
  C:0D00H         PUBLIC        _LCD9648_Write16CnCHAR
  C:0DC3H         PUBLIC        _LCD9648_Write16EnCHAR
  B:00A0H.6       PUBLIC        SCL
  D:00C8H         PUBLIC        T2CON
  C:11F9H         PUBLIC        _SendDataSPI
  B:0080H.1       PUBLIC        RST
  C:1138H         PUBLIC        LCD9648_Clear
  D:00D0H         PUBLIC        PSW
  -------         PROC          _SENDDATASPI
  D:0007H         SYMBOL        dat
  -------         DO            
  D:0006H         SYMBOL        i
  -------         ENDDO         
  C:11F9H         LINE#         4
  C:11F9H         LINE#         5
  C:11F9H         LINE#         8
  C:11FBH         LINE#         9
  C:11FBH         LINE#         10
  C:1203H         LINE#         11
  C:1205H         LINE#         13
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  17:50:05  PAGE 9


  C:1209H         LINE#         15
  C:120BH         LINE#         16
  C:120DH         LINE#         17
  C:1211H         LINE#         18
  -------         ENDPROC       _SENDDATASPI
  C:1227H         SYMBOL        L?0067
  C:1229H         SYMBOL        L?0068
  -------         PROC          L?0066
  -------         ENDPROC       L?0066
  C:1227H         SYMBOL        L?0067
  C:1229H         SYMBOL        L?0068
  -------         PROC          _WRITECOMM
  D:0006H         SYMBOL        i
  C:122DH         LINE#         20
  C:122DH         LINE#         21
  C:122DH         LINE#         23
  C:122FH         LINE#         24
  C:1231H         LINE#         26
  C:1234H         LINE#         28
  C:1236H         LINE#         30
  -------         ENDPROC       _WRITECOMM
  C:11C9H         SYMBOL        L?0070
  -------         PROC          L?0069
  -------         ENDPROC       L?0069
  C:11C9H         SYMBOL        L?0070
  -------         PROC          _WRITEDATA
  D:0006H         SYMBOL        i
  C:11D5H         LINE#         31
  C:11D5H         LINE#         32
  C:11D5H         LINE#         33
  C:11D7H         LINE#         34
  C:11D9H         LINE#         36
  C:11DCH         LINE#         38
  C:11DEH         LINE#         39
  -------         ENDPROC       _WRITEDATA
  -------         PROC          LCD9648_INIT
  -------         DO            
  D:0006H         SYMBOL        i
  -------         ENDDO         
  C:0FFAH         LINE#         41
  C:0FFAH         LINE#         42
  C:0FFAH         LINE#         46
  C:0FFCH         LINE#         47
  C:100AH         LINE#         49
  C:100CH         LINE#         50
  C:101AH         LINE#         52
  C:101CH         LINE#         53
  C:102AH         LINE#         55
  C:1031H         LINE#         56
  C:1038H         LINE#         57
  C:103FH         LINE#         58
  C:1046H         LINE#         59
  C:104DH         LINE#         60
  C:1054H         LINE#         61
  C:105BH         LINE#         62
  -------         ENDPROC       LCD9648_INIT
  -------         PROC          LCD9648_CLEAR
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  17:50:05  PAGE 10


  -------         DO            
  D:0005H         SYMBOL        i
  D:0004H         SYMBOL        j
  -------         ENDDO         
  C:1138H         LINE#         67
  C:1138H         LINE#         68
  C:1138H         LINE#         71
  C:113AH         LINE#         72
  C:113AH         LINE#         73
  C:1141H         LINE#         74
  C:1147H         LINE#         75
  C:114EH         LINE#         76
  C:1154H         LINE#         78
  C:1156H         LINE#         79
  C:1156H         LINE#         80
  C:115CH         LINE#         81
  C:1160H         LINE#         82
  C:1164H         LINE#         83
  -------         ENDPROC       LCD9648_CLEAR
  -------         PROC          _LCD9648_WRITE16CNCHAR
  D:0012H         SYMBOL        x
  D:0013H         SYMBOL        y
  D:0001H         SYMBOL        cn
  -------         DO            
  D:0004H         SYMBOL        j
  D:0014H         SYMBOL        x1
  D:0015H         SYMBOL        x2
  D:0005H         SYMBOL        wordNum
  -------         ENDDO         
  C:0D00H         LINE#         86
  C:0D04H         LINE#         87
  C:0D04H         LINE#         91
  C:0D0BH         LINE#         92
  C:0D0BH         LINE#         93
  C:0D0EH         LINE#         94
  C:0D0EH         LINE#         97
  C:0D15H         LINE#         98
  C:0D15H         LINE#         99
  C:0D18H         LINE#         100
  C:0D18H         LINE#         101
  C:0D1EH         LINE#         103
  C:0D24H         LINE#         104
  C:0D2CH         LINE#         105
  C:0D2CH         LINE#         108
  C:0D33H         LINE#         110
  C:0D3AH         LINE#         111
  C:0D40H         LINE#         113
  C:0D43H         LINE#         114
  C:0D4AH         LINE#         115
  C:0D4CH         LINE#         116
  C:0D4CH         LINE#         118
  C:0D7FH         LINE#         120
  C:0D7FH         LINE#         121
  C:0D80H         LINE#         122
  C:0D80H         LINE#         123
  C:0D85H         LINE#         124
  C:0D85H         LINE#         126
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  17:50:05  PAGE 11


  C:0D8CH         LINE#         129
  C:0D8FH         LINE#         130
  C:0D96H         LINE#         131
  C:0D96H         LINE#         132
  C:0DA6H         LINE#         133
  C:0DAAH         LINE#         134
  C:0DB0H         LINE#         135
  C:0DB0H         LINE#         136
  C:0DB6H         LINE#         137
  C:0DBDH         LINE#         139
  C:0DC0H         LINE#         140
  C:0DC2H         LINE#         141
  -------         ENDPROC       _LCD9648_WRITE16CNCHAR
  -------         PROC          _LCD9648_WRITE16ENCHAR
  D:0012H         SYMBOL        x
  D:0013H         SYMBOL        y
  D:0001H         SYMBOL        en
  -------         DO            
  D:0004H         SYMBOL        j
  D:0014H         SYMBOL        x1
  D:0015H         SYMBOL        x2
  D:0005H         SYMBOL        wordNum
  -------         ENDDO         
  C:0DC3H         LINE#         143
  C:0DC7H         LINE#         144
  C:0DC7H         LINE#         148
  C:0DCEH         LINE#         149
  C:0DCEH         LINE#         150
  C:0DD1H         LINE#         151
  C:0DD1H         LINE#         154
  C:0DD8H         LINE#         155
  C:0DD8H         LINE#         156
  C:0DDBH         LINE#         157
  C:0DDBH         LINE#         158
  C:0DE1H         LINE#         160
  C:0DE7H         LINE#         161
  C:0DEFH         LINE#         162
  C:0DEFH         LINE#         165
  C:0DF6H         LINE#         167
  C:0DFDH         LINE#         168
  C:0E03H         LINE#         170
  C:0E08H         LINE#         171
  C:0E0FH         LINE#         172
  C:0E11H         LINE#         173
  C:0E11H         LINE#         175
  C:0E29H         LINE#         176
  C:0E29H         LINE#         177
  C:0E2AH         LINE#         178
  C:0E2AH         LINE#         179
  C:0E2FH         LINE#         180
  C:0E2FH         LINE#         182
  C:0E36H         LINE#         185
  C:0E3BH         LINE#         186
  C:0E42H         LINE#         187
  C:0E42H         LINE#         188
  C:0E52H         LINE#         189
  C:0E56H         LINE#         190
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  17:50:05  PAGE 12


  C:0E5CH         LINE#         191
  C:0E5CH         LINE#         192
  C:0E62H         LINE#         193
  C:0E69H         LINE#         195
  C:0E6CH         LINE#         196
  C:0E6EH         LINE#         197
  -------         ENDPROC       _LCD9648_WRITE16ENCHAR
  -------         PROC          _LCD9648_WRITE16ENCHAR1
  D:0001H         SYMBOL        x
  D:0005H         SYMBOL        y
  D:0003H         SYMBOL        en
  -------         DO            
  D:0002H         SYMBOL        j
  D:0016H         SYMBOL        x1
  D:0017H         SYMBOL        x2
  D:0004H         SYMBOL        wordNum
  -------         ENDDO         
  C:0E6FH         LINE#         198
  C:0E71H         LINE#         199
  C:0E71H         LINE#         203
  C:0E77H         LINE#         204
  C:0E77H         LINE#         205
  C:0E7AH         LINE#         206
  C:0E7AH         LINE#         209
  C:0E80H         LINE#         210
  C:0E80H         LINE#         211
  C:0E83H         LINE#         212
  C:0E83H         LINE#         213
  C:0E87H         LINE#         215
  C:0E8DH         LINE#         217
  C:0E8DH         LINE#         220
  C:0E94H         LINE#         222
  C:0E9AH         LINE#         223
  C:0E9FH         LINE#         225
  C:0EA4H         LINE#         226
  C:0EABH         LINE#         227
  C:0EADH         LINE#         228
  C:0EADH         LINE#         230
  C:0EC1H         LINE#         231
  C:0EC1H         LINE#         232
  C:0EC2H         LINE#         233
  C:0EC2H         LINE#         234
  C:0EC7H         LINE#         235
  C:0EC7H         LINE#         237
  C:0ECDH         LINE#         240
  C:0ED2H         LINE#         241
  C:0ED9H         LINE#         242
  C:0ED9H         LINE#         243
  C:0EEEH         LINE#         244
  C:0EF2H         LINE#         245
  C:0EF6H         LINE#         246
  C:0EF6H         LINE#         247
  C:0EFAH         LINE#         248
  C:0EFAH         LINE#         250
  C:0EFAH         LINE#         251
  C:0EFCH         LINE#         252
  -------         ENDPROC       _LCD9648_WRITE16ENCHAR1
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  17:50:05  PAGE 13


  -------         ENDMOD        LCD9648

  -------         MODULE        ?C?FPMUL
  C:098DH         PUBLIC        ?C?FPMUL
  -------         ENDMOD        ?C?FPMUL

  -------         MODULE        ?C?FCAST
  C:0A9DH         PUBLIC        ?C?FCASTC
  C:0A98H         PUBLIC        ?C?FCASTI
  C:0A93H         PUBLIC        ?C?FCASTL
  -------         ENDMOD        ?C?FCAST

  -------         MODULE        ?C?CLDPTR
  C:0B1BH         PUBLIC        ?C?CLDPTR
  -------         ENDMOD        ?C?CLDPTR

  -------         MODULE        ?C?CLDOPTR
  C:0B34H         PUBLIC        ?C?CLDOPTR
  -------         ENDMOD        ?C?CLDOPTR

  -------         MODULE        ?C?LNEG
  C:0B61H         PUBLIC        ?C?LNEG
  -------         ENDMOD        ?C?LNEG

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?DS18B20_INIT?DS18B20

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?DS18B20_READ_TEMPERTURE?DS18B20

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?_LCD9648_WRITE16ENCHAR1?LCD9648

Program Size: data=25.0 xdata=0 code=4710
LINK/LOCATE RUN COMPLETE.  3 WARNING(S),  0 ERROR(S)
