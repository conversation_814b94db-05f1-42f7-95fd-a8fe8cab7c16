BL<PERSON> BANKED LINKER/LOCATER V6.22                                                        07/07/2025  22:55:07  PAGE 1


BL51 BANKED LINKER/LOCATER V6.22, INVOKED BY:
E:\KEIL\C51\BIN\BL51.EXE .\Objects\main.obj, .\Objects\ds18b20.obj, .\Objects\lcd9648.obj, .\Objects\Key.obj TO .\Object
>> s\Project PRINT (.\Listings\Project.m51) RAMSIZE (256)


MEMORY MODEL: SMALL WITH FLOATING POINT ARITHMETIC


INPUT MODULES INCLUDED:
  .\Objects\main.obj (MAIN)
  .\Objects\ds18b20.obj (DS18B20)
  .\Objects\lcd9648.obj (LCD9648)
  .\Objects\Key.obj (KEY)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?FPMUL)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?FCAST)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?FPGETOPN)
  E:\KEIL\C51\LIB\C51S.LIB (?C_STARTUP)
  E:\KEIL\C51\LIB\C51S.LIB (?C?CLDPTR)
  E:\KEIL\C51\LIB\C51S.LIB (?C?CLDOPTR)
  E:\KEIL\C51\LIB\C51S.LIB (?C?LNEG)
  E:\KEIL\C51\LIB\C51S.LIB (?C_INIT)


LINK MAP OF MODULE:  .\Objects\Project (MAIN)


            TYPE    BASE      LENGTH    RELOCATION   SEGMENT NAME
            -----------------------------------------------------

            * * * * * * *   D A T A   M E M O R Y   * * * * * * *
            REG     0000H     0008H     ABSOLUTE     "REG BANK 0"
            DATA    0008H     0011H     UNIT         ?DT?MAIN
            DATA    0019H     0006H     UNIT         ?DT?DS18B20_READ_TEMPERTURE?DS18B20
                    001FH     0001H                  *** GAP ***
            BIT     0020H.0   0000H.1   UNIT         ?BI?MAIN
                    0020H.1   0000H.7                *** GAP ***
            DATA    0021H     0009H     UNIT         _DATA_GROUP_
            DATA    002AH     0002H     UNIT         ?DT?_LCD9648_WRITE16ENCHAR1?LCD9648
            IDATA   002CH     0001H     UNIT         ?STACK

            * * * * * * *   C O D E   M E M O R Y   * * * * * * *
            CODE    0000H     0003H     ABSOLUTE     
            CODE    0003H     0008H     UNIT         ?PR?DS18B20_START?DS18B20
            CODE    000BH     0003H     ABSOLUTE     
            CODE    000EH     0891H     UNIT         ?CO?LCD9648
            CODE    089FH     01E2H     UNIT         ?C?LIB_CODE
            CODE    0A81H     0191H     UNIT         ?PR?LCD9648_INIT_PROC?MAIN
            CODE    0C12H     0189H     UNIT         ?PR?LCD_PROC?MAIN
            CODE    0D9BH     010DH     UNIT         ?PR?KEY_PROC?MAIN
            CODE    0EA8H     00C3H     UNIT         ?PR?_LCD9648_WRITE16CNCHAR?LCD9648
            CODE    0F6BH     00ACH     UNIT         ?PR?_LCD9648_WRITE16ENCHAR?LCD9648
            CODE    1017H     008EH     UNIT         ?PR?_LCD9648_WRITE16ENCHAR1?LCD9648
            CODE    10A5H     008CH     UNIT         ?C_C51STARTUP
            CODE    1131H     0071H     UNIT         ?PR?DS18B20_READ_TEMPERTURE?DS18B20
            CODE    11A2H     006CH     UNIT         ?CO?MAIN
            CODE    120EH     0068H     UNIT         ?PR?LCD9648_INIT?LCD9648
            CODE    1276H     003BH     UNIT         ?PR?DS18B20_CHECK?DS18B20
            CODE    12B1H     0034H     UNIT         ?PR?_DS18B20_WRITE_BYTE?DS18B20
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  22:55:07  PAGE 2


            CODE    12E5H     0033H     UNIT         ?PR?TIMER0_ISR?MAIN
            CODE    1318H     002DH     UNIT         ?PR?LCD9648_CLEAR?LCD9648
            CODE    1345H     0020H     UNIT         ?PR?LCD_INIT_TEST?MAIN
            CODE    1365H     001CH     UNIT         ?C_INITSEG
            CODE    1381H     001BH     UNIT         ?PR?DS18B20_READ_BYTE?DS18B20
            CODE    139CH     001BH     UNIT         ?PR?_WRITEDATA?LCD9648
            CODE    13B7H     001AH     UNIT         ?PR?DS18B20_READ_BIT?DS18B20
            CODE    13D1H     0019H     UNIT         ?PR?_SENDDATASPI?LCD9648
            CODE    13EAH     0016H     UNIT         ?PR?KEY_READ?KEY
            CODE    1400H     0013H     UNIT         ?PR?TIMER0_INIT?MAIN
            CODE    1413H     0012H     UNIT         ?PR?_WRITECOMM?LCD9648
            CODE    1425H     0011H     UNIT         ?PR?MAIN?MAIN
            CODE    1436H     000FH     UNIT         ?PR?_DELAY_10US?DS18B20
            CODE    1445H     000EH     UNIT         ?PR?DS18B20_RESET?DS18B20
            CODE    1453H     0007H     UNIT         ?PR?DS18B20_INIT?DS18B20



OVERLAY MAP OF MODULE:   .\Objects\Project (MAIN)


SEGMENT                                       DATA_GROUP 
  +--> CALLED SEGMENT                       START    LENGTH
-----------------------------------------------------------
?C_C51STARTUP                               -----    -----
  +--> ?PR?MAIN?MAIN
  +--> ?C_INITSEG

?PR?MAIN?MAIN                               -----    -----
  +--> ?PR?LCD9648_INIT_PROC?MAIN
  +--> ?PR?TIMER0_INIT?MAIN
  +--> ?PR?LCD_INIT_TEST?MAIN
  +--> ?PR?KEY_PROC?MAIN
  +--> ?PR?LCD_PROC?MAIN

?PR?LCD9648_INIT_PROC?MAIN                  -----    -----
  +--> ?PR?LCD9648_INIT?LCD9648
  +--> ?PR?LCD9648_CLEAR?LCD9648
  +--> ?CO?MAIN
  +--> ?PR?_LCD9648_WRITE16CNCHAR?LCD9648
  +--> ?PR?_LCD9648_WRITE16ENCHAR?LCD9648

?PR?LCD9648_INIT?LCD9648                    -----    -----
  +--> ?PR?_WRITECOMM?LCD9648

?PR?_WRITECOMM?LCD9648                      -----    -----
  +--> ?PR?_SENDDATASPI?LCD9648

?PR?LCD9648_CLEAR?LCD9648                   -----    -----
  +--> ?PR?_WRITECOMM?LCD9648
  +--> ?PR?_WRITEDATA?LCD9648

?PR?_WRITEDATA?LCD9648                      -----    -----
  +--> ?PR?_SENDDATASPI?LCD9648

?PR?_LCD9648_WRITE16CNCHAR?LCD9648          0021H    0004H
  +--> ?PR?_WRITECOMM?LCD9648
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  22:55:07  PAGE 3


  +--> ?CO?LCD9648
  +--> ?PR?_WRITEDATA?LCD9648

?PR?_LCD9648_WRITE16ENCHAR?LCD9648          0026H    0004H
  +--> ?PR?_WRITECOMM?LCD9648
  +--> ?CO?LCD9648
  +--> ?PR?_WRITEDATA?LCD9648

?PR?LCD_INIT_TEST?MAIN                      -----    -----
  +--> ?PR?LCD9648_INIT_PROC?MAIN

?PR?KEY_PROC?MAIN                           -----    -----
  +--> ?PR?KEY_READ?KEY

?PR?LCD_PROC?MAIN                           0021H    0005H
  +--> ?CO?MAIN
  +--> ?PR?_LCD9648_WRITE16ENCHAR?LCD9648



SYMBOL TABLE OF MODULE:  .\Objects\Project (MAIN)

  VALUE           TYPE          NAME
  ----------------------------------

  -------         MODULE        MAIN
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0080H         PUBLIC        P0
  D:0090H         PUBLIC        P1
  D:00A0H         PUBLIC        P2
  D:00B0H         PUBLIC        P3
  C:12E5H         PUBLIC        Timer0_ISR
  B:00A8H.7       PUBLIC        EA
  D:00A8H         PUBLIC        IE
  D:0008H         PUBLIC        Temperature
  D:00B8H         PUBLIC        IP
  C:0A81H         PUBLIC        LCD9648_Init_Proc
  B:0020H.0       PUBLIC        LCD_Init_End_Flag
  C:0D9BH         PUBLIC        Key_Proc
  D:0009H         PUBLIC        Key_Down
  C:1425H         PUBLIC        main
  C:1400H         PUBLIC        Timer0_Init
  D:000AH         PUBLIC        Key_Old
  D:000BH         PUBLIC        Key_Slow_Down
  D:000CH         PUBLIC        LCD_Disp_Mode
  D:000DH         PUBLIC        Key_Val
  D:000EH         PUBLIC        LCD_Init_Mode
  D:0098H         PUBLIC        SCON
  D:0089H         PUBLIC        TMOD
  D:0088H         PUBLIC        TCON
  D:000FH         PUBLIC        Timer0_count
  B:00A8H.1       PUBLIC        ET0
  C:0C12H         PUBLIC        LCD_Proc
  D:008CH         PUBLIC        TH0
  C:1345H         PUBLIC        LCD_Init_Test
  D:0011H         PUBLIC        Key_Up
  D:008AH         PUBLIC        TL0
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  22:55:07  PAGE 4


  B:0088H.4       PUBLIC        TR0
  D:0012H         PUBLIC        Time_Flag_Count
  D:0013H         PUBLIC        State_Index
  D:00C8H         PUBLIC        T2CON
  D:0014H         PUBLIC        Time_Flag
  D:0015H         PUBLIC        State_Disp
  D:00D0H         PUBLIC        PSW
  -------         PROC          KEY_PROC
  C:0D9BH         LINE#         25
  C:0D9BH         LINE#         26
  C:0D9BH         LINE#         27
  C:0DA2H         LINE#         28
  C:0DA5H         LINE#         30
  C:0DAAH         LINE#         31
  C:0DB3H         LINE#         32
  C:0DB9H         LINE#         33
  C:0DBCH         LINE#         35
  C:0DD5H         LINE#         36
  C:0DD5H         LINE#         37
  C:0DD5H         LINE#         38
  C:0DDEH         LINE#         39
  C:0DDEH         LINE#         40
  C:0DE2H         LINE#         41
  C:0DE2H         LINE#         42
  C:0DECH         LINE#         43
  C:0DF3H         LINE#         44
  C:0DF3H         LINE#         46
  C:0DF8H         LINE#         47
  C:0DF8H         LINE#         48
  C:0E00H         LINE#         49
  C:0E07H         LINE#         50
  C:0E07H         LINE#         52
  C:0E0CH         LINE#         53
  C:0E0CH         LINE#         54
  C:0E14H         LINE#         55
  C:0E1BH         LINE#         56
  C:0E1BH         LINE#         58
  C:0E24H         LINE#         59
  C:0E24H         LINE#         60
  C:0E2FH         LINE#         61
  C:0E2FH         LINE#         62
  C:0E2FH         LINE#         63
  C:0E2FH         LINE#         64
  C:0E31H         LINE#         66
  C:0E31H         LINE#         67
  C:0E37H         LINE#         68
  C:0E37H         LINE#         69
  C:0E3BH         LINE#         70
  C:0E3BH         LINE#         71
  C:0E45H         LINE#         72
  C:0E4CH         LINE#         73
  C:0E4CH         LINE#         75
  C:0E51H         LINE#         76
  C:0E51H         LINE#         77
  C:0E59H         LINE#         78
  C:0E60H         LINE#         79
  C:0E60H         LINE#         81
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  22:55:07  PAGE 5


  C:0E65H         LINE#         82
  C:0E65H         LINE#         83
  C:0E6DH         LINE#         84
  C:0E74H         LINE#         85
  C:0E74H         LINE#         87
  C:0E7AH         LINE#         88
  C:0E7AH         LINE#         89
  C:0E85H         LINE#         90
  C:0E8CH         LINE#         91
  C:0E8CH         LINE#         92
  C:0E8CH         LINE#         93
  C:0E8DH         LINE#         95
  C:0E8DH         LINE#         96
  C:0E97H         LINE#         97
  C:0E98H         LINE#         99
  C:0E98H         LINE#         100
  C:0E9DH         LINE#         101
  C:0E9DH         LINE#         102
  C:0EA4H         LINE#         103
  C:0EA7H         LINE#         104
  C:0EA7H         LINE#         105
  C:0EA7H         LINE#         106
  C:0EA7H         LINE#         107
  -------         ENDPROC       KEY_PROC
  -------         PROC          TIMER0_INIT
  C:1400H         LINE#         118
  C:1400H         LINE#         119
  C:1400H         LINE#         120
  C:1403H         LINE#         121
  C:1406H         LINE#         122
  C:1409H         LINE#         123
  C:140CH         LINE#         124
  C:140EH         LINE#         125
  C:1410H         LINE#         126
  C:1412H         LINE#         127
  -------         ENDPROC       TIMER0_INIT
  -------         PROC          TIMER0_ISR
  C:12E5H         LINE#         133
  C:12E9H         LINE#         135
  C:12ECH         LINE#         136
  C:12EFH         LINE#         138
  C:12F7H         LINE#         140
  C:1300H         LINE#         141
  C:1300H         LINE#         142
  C:1303H         LINE#         143
  C:1309H         LINE#         145
  C:1309H         LINE#         147
  C:1313H         LINE#         148
  -------         ENDPROC       TIMER0_ISR
  -------         PROC          LCD9648_INIT_PROC
  C:0A81H         LINE#         149
  C:0A81H         LINE#         150
  C:0A81H         LINE#         151
  C:0A97H         LINE#         152
  C:0A97H         LINE#         153
  C:0A97H         LINE#         154
  C:0A9AH         LINE#         155
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  22:55:07  PAGE 6


  C:0A9DH         LINE#         157
  C:0AA9H         LINE#         158
  C:0AB6H         LINE#         159
  C:0AC3H         LINE#         161
  C:0AD0H         LINE#         163
  C:0ADDH         LINE#         164
  C:0AEAH         LINE#         165
  C:0AF7H         LINE#         166
  C:0B04H         LINE#         167
  C:0B11H         LINE#         168
  C:0B11H         LINE#         170
  C:0B11H         LINE#         171
  C:0B14H         LINE#         172
  C:0B17H         LINE#         174
  C:0B23H         LINE#         175
  C:0B30H         LINE#         176
  C:0B3DH         LINE#         177
  C:0B4AH         LINE#         178
  C:0B57H         LINE#         179
  C:0B64H         LINE#         181
  C:0B71H         LINE#         182
  C:0B7EH         LINE#         183
  C:0B8BH         LINE#         184
  C:0B98H         LINE#         185
  C:0BA5H         LINE#         186
  C:0BB2H         LINE#         188
  C:0BBFH         LINE#         189
  C:0BCCH         LINE#         190
  C:0BD9H         LINE#         191
  C:0BE3H         LINE#         192
  C:0BE5H         LINE#         194
  C:0BE5H         LINE#         195
  C:0BE8H         LINE#         196
  C:0BEBH         LINE#         199
  C:0BF7H         LINE#         200
  C:0C04H         LINE#         201
  C:0C11H         LINE#         204
  C:0C11H         LINE#         206
  C:0C11H         LINE#         208
  C:0C11H         LINE#         209
  C:0C11H         LINE#         210
  -------         ENDPROC       LCD9648_INIT_PROC
  -------         PROC          LCD_INIT_TEST
  C:1345H         LINE#         212
  C:1345H         LINE#         213
  C:1345H         LINE#         214
  C:1349H         LINE#         215
  C:1349H         LINE#         216
  C:134CH         LINE#         217
  C:134EH         LINE#         218
  C:1350H         LINE#         219
  C:1357H         LINE#         220
  C:1357H         LINE#         221
  C:135AH         LINE#         222
  C:135AH         LINE#         223
  C:135FH         LINE#         224
  C:1364H         LINE#         225
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  22:55:07  PAGE 7


  C:1364H         LINE#         227
  -------         ENDPROC       LCD_INIT_TEST
  -------         PROC          LCD_PROC
  -------         DO            
  D:0021H         SYMBOL        temp_str
  D:0025H         SYMBOL        i
  -------         ENDDO         
  C:0C12H         LINE#         235
  C:0C12H         LINE#         236
  C:0C12H         LINE#         240
  C:0C18H         LINE#         241
  C:0C18H         LINE#         242
  C:0C1FH         LINE#         243
  C:0C1FH         LINE#         245
  C:0C2AH         LINE#         246
  C:0C37H         LINE#         247
  C:0C44H         LINE#         248
  C:0C51H         LINE#         252
  C:0C5BH         LINE#         253
  C:0C67H         LINE#         254
  C:0C6AH         LINE#         255
  C:0C6DH         LINE#         256
  C:0C78H         LINE#         259
  C:0C82H         LINE#         260
  C:0C8EH         LINE#         261
  C:0C91H         LINE#         262
  C:0C94H         LINE#         263
  C:0CA0H         LINE#         266
  C:0CAAH         LINE#         267
  C:0CB6H         LINE#         268
  C:0CB9H         LINE#         269
  C:0CBCH         LINE#         270
  C:0CC5H         LINE#         278
  C:0CC8H         LINE#         279
  C:0CD1H         LINE#         280
  C:0CD1H         LINE#         282
  C:0CD3H         LINE#         283
  C:0CD3H         LINE#         284
  C:0CD8H         LINE#         285
  C:0CD8H         LINE#         286
  C:0CE1H         LINE#         287
  C:0CE3H         LINE#         289
  C:0CE3H         LINE#         290
  C:0CF3H         LINE#         291
  C:0CF3H         LINE#         292
  C:0CFCH         LINE#         295
  C:0D09H         LINE#         296
  C:0D13H         LINE#         297
  C:0D1FH         LINE#         298
  C:0D22H         LINE#         299
  C:0D25H         LINE#         300
  C:0D30H         LINE#         302
  C:0D3DH         LINE#         303
  C:0D47H         LINE#         304
  C:0D53H         LINE#         305
  C:0D56H         LINE#         306
  C:0D59H         LINE#         307
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  22:55:07  PAGE 8


  C:0D65H         LINE#         309
  C:0D72H         LINE#         310
  C:0D7CH         LINE#         311
  C:0D88H         LINE#         312
  C:0D8BH         LINE#         313
  C:0D8EH         LINE#         314
  C:0D9AH         LINE#         322
  C:0D9AH         LINE#         323
  C:0D9AH         LINE#         324
  -------         ENDPROC       LCD_PROC
  -------         PROC          MAIN
  C:1425H         LINE#         327
  C:1425H         LINE#         328
  C:1425H         LINE#         329
  C:1428H         LINE#         330
  C:142BH         LINE#         332
  C:142BH         LINE#         333
  C:142BH         LINE#         334
  C:142EH         LINE#         336
  C:1431H         LINE#         337
  C:1434H         LINE#         338
  -------         ENDPROC       MAIN
  -------         ENDMOD        MAIN

  -------         MODULE        DS18B20
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  C:1445H         PUBLIC        ds18b20_reset
  D:00A8H         PUBLIC        IE
  C:0003H         PUBLIC        ds18b20_start
  D:00B8H         PUBLIC        IP
  C:1381H         PUBLIC        ds18b20_read_byte
  C:1453H         PUBLIC        ds18b20_init
  C:13B7H         PUBLIC        ds18b20_read_bit
  C:12B9H         PUBLIC        _ds18b20_write_byte
  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
  C:1131H         PUBLIC        ds18b20_read_temperture
  C:1436H         PUBLIC        _delay_10us
  B:00B0H.7       PUBLIC        DS18B20_PORT
  D:00C8H         PUBLIC        T2CON
  C:1276H         PUBLIC        ds18b20_check
  D:00D0H         PUBLIC        PSW
  -------         PROC          _DELAY_10US
  D:0007H         SYMBOL        xms
  -------         DO            
  D:0006H         SYMBOL        i
  -------         ENDDO         
  C:1436H         LINE#         4
  C:1436H         LINE#         5
  C:1436H         LINE#         7
  C:143CH         LINE#         8
  C:143CH         LINE#         9
  C:143EH         LINE#         10
  C:1444H         LINE#         12
  -------         ENDPROC       _DELAY_10US
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  22:55:07  PAGE 9


  -------         PROC          DS18B20_RESET
  C:1445H         LINE#         20
  C:1445H         LINE#         21
  C:1445H         LINE#         22
  C:1447H         LINE#         23
  C:144CH         LINE#         24
  C:144EH         LINE#         25
  -------         ENDPROC       DS18B20_RESET
  -------         PROC          DS18B20_CHECK
  -------         DO            
  D:0004H         SYMBOL        time_temp
  -------         ENDDO         
  C:1276H         LINE#         34
  C:1276H         LINE#         35
  C:1276H         LINE#         36
  C:1278H         LINE#         38
  C:1281H         LINE#         39
  C:1281H         LINE#         40
  C:1282H         LINE#         41
  C:1287H         LINE#         42
  C:1289H         LINE#         43
  C:1292H         LINE#         44
  C:1294H         LINE#         45
  C:129DH         LINE#         46
  C:129DH         LINE#         47
  C:129EH         LINE#         48
  C:12A3H         LINE#         49
  C:12A5H         LINE#         50
  C:12AEH         LINE#         51
  C:12B0H         LINE#         52
  -------         ENDPROC       DS18B20_CHECK
  -------         PROC          DS18B20_READ_BIT
  -------         DO            
  D:0004H         SYMBOL        dat
  -------         ENDDO         
  C:13B7H         LINE#         60
  C:13B7H         LINE#         61
  C:13B7H         LINE#         62
  C:13B9H         LINE#         64
  C:13BBH         LINE#         65
  C:13BDH         LINE#         66
  C:13BFH         LINE#         67
  C:13C1H         LINE#         68
  C:13C7H         LINE#         69
  C:13C9H         LINE#         70
  C:13CEH         LINE#         71
  C:13D0H         LINE#         72
  -------         ENDPROC       DS18B20_READ_BIT
  -------         PROC          DS18B20_READ_BYTE
  -------         DO            
  D:0003H         SYMBOL        i
  D:0002H         SYMBOL        dat
  D:0007H         SYMBOL        temp
  -------         ENDDO         
  C:1381H         LINE#         80
  C:1381H         LINE#         81
  C:1381H         LINE#         82
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  22:55:07  PAGE 10


  C:1383H         LINE#         83
  C:1384H         LINE#         84
  C:1385H         LINE#         86
  C:1385H         LINE#         87
  C:1385H         LINE#         88
  C:1388H         LINE#         89
  C:1395H         LINE#         90
  C:1399H         LINE#         91
  C:139BH         LINE#         92
  -------         ENDPROC       DS18B20_READ_BYTE
  -------         PROC          L?0035
  -------         ENDPROC       L?0035
  -------         PROC          _DS18B20_WRITE_BYTE
  D:0004H         SYMBOL        dat
  -------         DO            
  D:0003H         SYMBOL        i
  D:0002H         SYMBOL        temp
  -------         ENDDO         
  C:12B9H         LINE#         100
  C:12BBH         LINE#         101
  C:12BBH         LINE#         102
  C:12BDH         LINE#         103
  C:12BDH         LINE#         105
  C:12BDH         LINE#         106
  C:12BDH         LINE#         107
  C:12C1H         LINE#         108
  C:12C5H         LINE#         109
  C:12C8H         LINE#         110
  C:12C8H         LINE#         111
  C:12CAH         LINE#         112
  C:12CCH         LINE#         113
  C:12CEH         LINE#         114
  C:12D3H         LINE#         115
  C:12D5H         LINE#         117
  C:12D5H         LINE#         118
  C:12D7H         LINE#         119
  C:12DCH         LINE#         120
  C:12DEH         LINE#         121
  C:12E0H         LINE#         122
  C:12E0H         LINE#         123
  C:12E4H         LINE#         124
  -------         ENDPROC       _DS18B20_WRITE_BYTE
  -------         PROC          DS18B20_START
  C:0003H         LINE#         132
  C:0003H         LINE#         133
  C:0003H         LINE#         134
  C:0003H         LINE#         135
  C:0003H         LINE#         136
  C:0006H         LINE#         137
  -------         ENDPROC       DS18B20_START
  -------         PROC          DS18B20_INIT
  C:1453H         LINE#         146
  C:1453H         LINE#         147
  C:1453H         LINE#         148
  C:1456H         LINE#         149
  C:1459H         LINE#         150
  -------         ENDPROC       DS18B20_INIT
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  22:55:07  PAGE 11


  -------         PROC          DS18B20_READ_TEMPERTURE
  -------         DO            
  D:0019H         SYMBOL        temp
  D:0007H         SYMBOL        dath
  D:0001H         SYMBOL        datl
  D:001DH         SYMBOL        value
  -------         ENDDO         
  C:1131H         LINE#         158
  C:1131H         LINE#         159
  C:1131H         LINE#         161
  C:1133H         LINE#         162
  C:1134H         LINE#         163
  C:1138H         LINE#         165
  C:113BH         LINE#         166
  C:113BH         LINE#         167
  C:113BH         LINE#         168
  C:113EH         LINE#         169
  C:1143H         LINE#         171
  C:1148H         LINE#         172
  C:114BH         LINE#         173
  C:1157H         LINE#         175
  C:115EH         LINE#         176
  C:115EH         LINE#         177
  C:116FH         LINE#         178
  C:117DH         LINE#         179
  C:117FH         LINE#         181
  C:117FH         LINE#         182
  C:1199H         LINE#         183
  C:1199H         LINE#         184
  C:11A1H         LINE#         185
  -------         ENDPROC       DS18B20_READ_TEMPERTURE
  -------         ENDMOD        DS18B20

  -------         MODULE        LCD9648
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0080H         PUBLIC        P0
  D:0090H         PUBLIC        P1
  D:00A0H         PUBLIC        P2
  D:00B0H         PUBLIC        P3
  C:13ADH         PUBLIC        _WriteData
  C:1017H         PUBLIC        _LCD9648_Write16EnCHAR1
  D:00A8H         PUBLIC        IE
  D:00B8H         PUBLIC        IP
  C:141BH         PUBLIC        _WriteComm
  B:00A0H.7       PUBLIC        RS
  C:120EH         PUBLIC        LCD9648_Init
  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
  B:0080H.0       PUBLIC        CS0
  C:000EH         PUBLIC        CN16CHARfont
  C:0250H         PUBLIC        EN16CHARfont
  B:00A0H.5       PUBLIC        SDA
  C:0EA8H         PUBLIC        _LCD9648_Write16CnCHAR
  C:0F6BH         PUBLIC        _LCD9648_Write16EnCHAR
  B:00A0H.6       PUBLIC        SCL
  D:00C8H         PUBLIC        T2CON
  C:13D1H         PUBLIC        _SendDataSPI
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  22:55:07  PAGE 12


  B:0080H.1       PUBLIC        RST
  C:1318H         PUBLIC        LCD9648_Clear
  D:00D0H         PUBLIC        PSW
  -------         PROC          _SENDDATASPI
  D:0007H         SYMBOL        dat
  -------         DO            
  D:0006H         SYMBOL        i
  -------         ENDDO         
  C:13D1H         LINE#         4
  C:13D1H         LINE#         5
  C:13D1H         LINE#         8
  C:13D3H         LINE#         9
  C:13D3H         LINE#         10
  C:13DBH         LINE#         11
  C:13DDH         LINE#         13
  C:13E1H         LINE#         15
  C:13E3H         LINE#         16
  C:13E5H         LINE#         17
  C:13E9H         LINE#         18
  -------         ENDPROC       _SENDDATASPI
  C:1415H         SYMBOL        L?0067
  C:1417H         SYMBOL        L?0068
  -------         PROC          L?0066
  -------         ENDPROC       L?0066
  C:1415H         SYMBOL        L?0067
  C:1417H         SYMBOL        L?0068
  -------         PROC          _WRITECOMM
  D:0006H         SYMBOL        i
  C:141BH         LINE#         20
  C:141BH         LINE#         21
  C:141BH         LINE#         23
  C:141DH         LINE#         24
  C:141FH         LINE#         26
  C:1422H         LINE#         28
  C:1424H         LINE#         30
  -------         ENDPROC       _WRITECOMM
  C:13A1H         SYMBOL        L?0070
  -------         PROC          L?0069
  -------         ENDPROC       L?0069
  C:13A1H         SYMBOL        L?0070
  -------         PROC          _WRITEDATA
  D:0006H         SYMBOL        i
  C:13ADH         LINE#         31
  C:13ADH         LINE#         32
  C:13ADH         LINE#         33
  C:13AFH         LINE#         34
  C:13B1H         LINE#         36
  C:13B4H         LINE#         38
  C:13B6H         LINE#         39
  -------         ENDPROC       _WRITEDATA
  -------         PROC          LCD9648_INIT
  -------         DO            
  D:0006H         SYMBOL        i
  -------         ENDDO         
  C:120EH         LINE#         41
  C:120EH         LINE#         42
  C:120EH         LINE#         46
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  22:55:07  PAGE 13


  C:1210H         LINE#         47
  C:121EH         LINE#         49
  C:1220H         LINE#         50
  C:122EH         LINE#         52
  C:1230H         LINE#         53
  C:123EH         LINE#         55
  C:1245H         LINE#         56
  C:124CH         LINE#         57
  C:1253H         LINE#         58
  C:125AH         LINE#         59
  C:1261H         LINE#         60
  C:1268H         LINE#         61
  C:126FH         LINE#         62
  -------         ENDPROC       LCD9648_INIT
  -------         PROC          LCD9648_CLEAR
  -------         DO            
  D:0005H         SYMBOL        i
  D:0004H         SYMBOL        j
  -------         ENDDO         
  C:1318H         LINE#         67
  C:1318H         LINE#         68
  C:1318H         LINE#         71
  C:131AH         LINE#         72
  C:131AH         LINE#         73
  C:1321H         LINE#         74
  C:1327H         LINE#         75
  C:132EH         LINE#         76
  C:1334H         LINE#         78
  C:1336H         LINE#         79
  C:1336H         LINE#         80
  C:133CH         LINE#         81
  C:1340H         LINE#         82
  C:1344H         LINE#         83
  -------         ENDPROC       LCD9648_CLEAR
  -------         PROC          _LCD9648_WRITE16CNCHAR
  D:0021H         SYMBOL        x
  D:0022H         SYMBOL        y
  D:0001H         SYMBOL        cn
  -------         DO            
  D:0004H         SYMBOL        j
  D:0023H         SYMBOL        x1
  D:0024H         SYMBOL        x2
  D:0005H         SYMBOL        wordNum
  -------         ENDDO         
  C:0EA8H         LINE#         86
  C:0EACH         LINE#         87
  C:0EACH         LINE#         91
  C:0EB3H         LINE#         92
  C:0EB3H         LINE#         93
  C:0EB6H         LINE#         94
  C:0EB6H         LINE#         97
  C:0EBDH         LINE#         98
  C:0EBDH         LINE#         99
  C:0EC0H         LINE#         100
  C:0EC0H         LINE#         101
  C:0EC6H         LINE#         103
  C:0ECCH         LINE#         104
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  22:55:07  PAGE 14


  C:0ED4H         LINE#         105
  C:0ED4H         LINE#         108
  C:0EDBH         LINE#         110
  C:0EE2H         LINE#         111
  C:0EE8H         LINE#         113
  C:0EEBH         LINE#         114
  C:0EF2H         LINE#         115
  C:0EF4H         LINE#         116
  C:0EF4H         LINE#         118
  C:0F27H         LINE#         120
  C:0F27H         LINE#         121
  C:0F28H         LINE#         122
  C:0F28H         LINE#         123
  C:0F2DH         LINE#         124
  C:0F2DH         LINE#         126
  C:0F34H         LINE#         129
  C:0F37H         LINE#         130
  C:0F3EH         LINE#         131
  C:0F3EH         LINE#         132
  C:0F4EH         LINE#         133
  C:0F52H         LINE#         134
  C:0F58H         LINE#         135
  C:0F58H         LINE#         136
  C:0F5EH         LINE#         137
  C:0F65H         LINE#         139
  C:0F68H         LINE#         140
  C:0F6AH         LINE#         141
  -------         ENDPROC       _LCD9648_WRITE16CNCHAR
  -------         PROC          _LCD9648_WRITE16ENCHAR
  D:0026H         SYMBOL        x
  D:0027H         SYMBOL        y
  D:0001H         SYMBOL        en
  -------         DO            
  D:0004H         SYMBOL        j
  D:0028H         SYMBOL        x1
  D:0029H         SYMBOL        x2
  D:0005H         SYMBOL        wordNum
  -------         ENDDO         
  C:0F6BH         LINE#         143
  C:0F6FH         LINE#         144
  C:0F6FH         LINE#         148
  C:0F76H         LINE#         149
  C:0F76H         LINE#         150
  C:0F79H         LINE#         151
  C:0F79H         LINE#         154
  C:0F80H         LINE#         155
  C:0F80H         LINE#         156
  C:0F83H         LINE#         157
  C:0F83H         LINE#         158
  C:0F89H         LINE#         160
  C:0F8FH         LINE#         161
  C:0F97H         LINE#         162
  C:0F97H         LINE#         165
  C:0F9EH         LINE#         167
  C:0FA5H         LINE#         168
  C:0FABH         LINE#         170
  C:0FB0H         LINE#         171
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  22:55:07  PAGE 15


  C:0FB7H         LINE#         172
  C:0FB9H         LINE#         173
  C:0FB9H         LINE#         175
  C:0FD1H         LINE#         176
  C:0FD1H         LINE#         177
  C:0FD2H         LINE#         178
  C:0FD2H         LINE#         179
  C:0FD7H         LINE#         180
  C:0FD7H         LINE#         182
  C:0FDEH         LINE#         185
  C:0FE3H         LINE#         186
  C:0FEAH         LINE#         187
  C:0FEAH         LINE#         188
  C:0FFAH         LINE#         189
  C:0FFEH         LINE#         190
  C:1004H         LINE#         191
  C:1004H         LINE#         192
  C:100AH         LINE#         193
  C:1011H         LINE#         195
  C:1014H         LINE#         196
  C:1016H         LINE#         197
  -------         ENDPROC       _LCD9648_WRITE16ENCHAR
  -------         PROC          _LCD9648_WRITE16ENCHAR1
  D:0001H         SYMBOL        x
  D:0005H         SYMBOL        y
  D:0003H         SYMBOL        en
  -------         DO            
  D:0002H         SYMBOL        j
  D:002AH         SYMBOL        x1
  D:002BH         SYMBOL        x2
  D:0004H         SYMBOL        wordNum
  -------         ENDDO         
  C:1017H         LINE#         198
  C:1019H         LINE#         199
  C:1019H         LINE#         203
  C:101FH         LINE#         204
  C:101FH         LINE#         205
  C:1022H         LINE#         206
  C:1022H         LINE#         209
  C:1028H         LINE#         210
  C:1028H         LINE#         211
  C:102BH         LINE#         212
  C:102BH         LINE#         213
  C:102FH         LINE#         215
  C:1035H         LINE#         217
  C:1035H         LINE#         220
  C:103CH         LINE#         222
  C:1042H         LINE#         223
  C:1047H         LINE#         225
  C:104CH         LINE#         226
  C:1053H         LINE#         227
  C:1055H         LINE#         228
  C:1055H         LINE#         230
  C:1069H         LINE#         231
  C:1069H         LINE#         232
  C:106AH         LINE#         233
  C:106AH         LINE#         234
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  22:55:07  PAGE 16


  C:106FH         LINE#         235
  C:106FH         LINE#         237
  C:1075H         LINE#         240
  C:107AH         LINE#         241
  C:1081H         LINE#         242
  C:1081H         LINE#         243
  C:1096H         LINE#         244
  C:109AH         LINE#         245
  C:109EH         LINE#         246
  C:109EH         LINE#         247
  C:10A2H         LINE#         248
  C:10A2H         LINE#         250
  C:10A2H         LINE#         251
  C:10A4H         LINE#         252
  -------         ENDPROC       _LCD9648_WRITE16ENCHAR1
  -------         ENDMOD        LCD9648

  -------         MODULE        KEY
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  C:13EAH         PUBLIC        Key_Read
  D:00A8H         PUBLIC        IE
  D:00B8H         PUBLIC        IP
  B:0090H.0       PUBLIC        k1
  B:0090H.1       PUBLIC        k2
  B:0090H.2       PUBLIC        k3
  B:0090H.3       PUBLIC        k4
  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
  D:00C8H         PUBLIC        T2CON
  D:00D0H         PUBLIC        PSW
  -------         PROC          KEY_READ
  -------         DO            
  D:0007H         SYMBOL        temp
  -------         ENDDO         
  C:13EAH         LINE#         4
  C:13EAH         LINE#         5
  C:13EAH         LINE#         6
  C:13ECH         LINE#         8
  C:13F0H         LINE#         9
  C:13F5H         LINE#         10
  C:13FAH         LINE#         11
  C:13FFH         LINE#         12
  C:13FFH         LINE#         13
  -------         ENDPROC       KEY_READ
  -------         ENDMOD        KEY

  -------         MODULE        ?C?FPMUL
  C:089FH         PUBLIC        ?C?FPMUL
  -------         ENDMOD        ?C?FPMUL

  -------         MODULE        ?C?FCAST
  C:09AFH         PUBLIC        ?C?FCASTC
  C:09AAH         PUBLIC        ?C?FCASTI
  C:09A5H         PUBLIC        ?C?FCASTL
  -------         ENDMOD        ?C?FCAST
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  22:55:07  PAGE 17



  -------         MODULE        ?C?CLDPTR
  C:0A2DH         PUBLIC        ?C?CLDPTR
  -------         ENDMOD        ?C?CLDPTR

  -------         MODULE        ?C?CLDOPTR
  C:0A46H         PUBLIC        ?C?CLDOPTR
  -------         ENDMOD        ?C?CLDOPTR

  -------         MODULE        ?C?LNEG
  C:0A73H         PUBLIC        ?C?LNEG
  -------         ENDMOD        ?C?LNEG

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?DS18B20_INIT?DS18B20

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?DS18B20_READ_TEMPERTURE?DS18B20

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?_LCD9648_WRITE16ENCHAR1?LCD9648

Program Size: data=43.1 xdata=0 code=5210
LINK/LOCATE RUN COMPLETE.  3 WARNING(S),  0 ERROR(S)
