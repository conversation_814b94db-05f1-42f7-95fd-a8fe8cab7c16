BL<PERSON> BANKED LINKER/LOCATER V6.22                                                        07/07/2025  23:06:27  PAGE 1


BL51 BANKED LINKER/LOCATER V6.22, INVOKED BY:
E:\KEIL\C51\BIN\BL51.EXE .\Objects\main.obj, .\Objects\ds18b20.obj, .\Objects\lcd9648.obj, .\Objects\Key.obj TO .\Object
>> s\Project PRINT (.\Listings\Project.m51) RAMSIZE (256)


MEMORY MODEL: SMALL WITH FLOATING POINT ARITHMETIC


INPUT MODULES INCLUDED:
  .\Objects\main.obj (MAIN)
  .\Objects\ds18b20.obj (DS18B20)
  .\Objects\lcd9648.obj (LCD9648)
  .\Objects\Key.obj (KEY)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?FPMUL)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?FCAST)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?FPGETOPN)
  E:\KEIL\C51\LIB\C51S.LIB (?C_STARTUP)
  E:\KEIL\C51\LIB\C51S.LIB (?C?CLDPTR)
  E:\KEIL\C51\LIB\C51S.LIB (?C?CLDOPTR)
  E:\KEIL\C51\LIB\C51S.LIB (?C?CSTPTR)
  E:\KEIL\C51\LIB\C51S.LIB (?C?CSTOPTR)
  E:\KEIL\C51\LIB\C51S.LIB (?C?UIDIV)
  E:\KEIL\C51\LIB\C51S.LIB (?C?LNEG)
  E:\KEIL\C51\LIB\C51S.LIB (?C_INIT)


LINK MAP OF MODULE:  .\Objects\Project (MAIN)


            TYPE    BASE      LENGTH    RELOCATION   SEGMENT NAME
            -----------------------------------------------------

            * * * * * * *   D A T A   M E M O R Y   * * * * * * *
            REG     0000H     0008H     ABSOLUTE     "REG BANK 0"
            DATA    0008H     0015H     UNIT         ?DT?MAIN
            DATA    001DH     0002H     UNIT         ?DT?_LCD9648_WRITE16ENCHAR1?LCD9648
                    001FH     0001H                  *** GAP ***
            BIT     0020H.0   0000H.1   UNIT         ?BI?MAIN
                    0020H.1   0000H.7                *** GAP ***
            DATA    0021H     000EH     UNIT         _DATA_GROUP_
            DATA    002FH     0006H     UNIT         ?DT?DS18B20_READ_TEMPERTURE?DS18B20
            IDATA   0035H     0001H     UNIT         ?STACK

            * * * * * * *   C O D E   M E M O R Y   * * * * * * *
            CODE    0000H     0003H     ABSOLUTE     
            CODE    0003H     0008H     UNIT         ?PR?DS18B20_START?DS18B20
            CODE    000BH     0003H     ABSOLUTE     
            CODE    000EH     0891H     UNIT         ?CO?LCD9648
            CODE    089FH     026BH     UNIT         ?C?LIB_CODE
            CODE    0B0AH     0204H     UNIT         ?PR?KEY_PROC?MAIN
            CODE    0D0EH     016CH     UNIT         ?PR?LCD9648_INIT_PROC?MAIN
            CODE    0E7AH     0133H     UNIT         ?PR?LCD_PROC?MAIN
            CODE    0FADH     00C3H     UNIT         ?PR?_LCD9648_WRITE16CNCHAR?LCD9648
            CODE    1070H     00BCH     UNIT         ?PR?_FLOAT_TO_STR?MAIN
            CODE    112CH     00ACH     UNIT         ?PR?_LCD9648_WRITE16ENCHAR?LCD9648
            CODE    11D8H     008EH     UNIT         ?PR?_LCD9648_WRITE16ENCHAR1?LCD9648
            CODE    1266H     008CH     UNIT         ?C_C51STARTUP
            CODE    12F2H     0071H     UNIT         ?PR?DS18B20_READ_TEMPERTURE?DS18B20
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  23:06:27  PAGE 2


            CODE    1363H     0068H     UNIT         ?PR?LCD9648_INIT?LCD9648
            CODE    13CBH     0064H     UNIT         ?CO?MAIN
            CODE    142FH     003BH     UNIT         ?PR?DS18B20_CHECK?DS18B20
            CODE    146AH     0034H     UNIT         ?PR?_DS18B20_WRITE_BYTE?DS18B20
            CODE    149EH     0033H     UNIT         ?PR?TIMER0_ISR?MAIN
            CODE    14D1H     002DH     UNIT         ?PR?LCD9648_CLEAR?LCD9648
            CODE    14FEH     0020H     UNIT         ?PR?LCD_INIT_TEST?MAIN
            CODE    151EH     0020H     UNIT         ?C_INITSEG
            CODE    153EH     001BH     UNIT         ?PR?DS18B20_READ_BYTE?DS18B20
            CODE    1559H     001BH     UNIT         ?PR?_WRITEDATA?LCD9648
            CODE    1574H     001AH     UNIT         ?PR?DS18B20_READ_BIT?DS18B20
            CODE    158EH     0019H     UNIT         ?PR?_SENDDATASPI?LCD9648
            CODE    15A7H     0016H     UNIT         ?PR?KEY_READ?KEY
            CODE    15BDH     0013H     UNIT         ?PR?TIMER0_INIT?MAIN
            CODE    15D0H     0012H     UNIT         ?PR?_WRITECOMM?LCD9648
            CODE    15E2H     0011H     UNIT         ?PR?MAIN?MAIN
            CODE    15F3H     000FH     UNIT         ?PR?_DELAY_10US?DS18B20
            CODE    1602H     000EH     UNIT         ?PR?DS18B20_RESET?DS18B20
            CODE    1610H     0007H     UNIT         ?PR?DS18B20_INIT?DS18B20



OVERLAY MAP OF MODULE:   .\Objects\Project (MAIN)


SEGMENT                                       DATA_GROUP 
  +--> CALLED SEGMENT                       START    LENGTH
-----------------------------------------------------------
?C_C51STARTUP                               -----    -----
  +--> ?PR?MAIN?MAIN
  +--> ?C_INITSEG

?PR?MAIN?MAIN                               -----    -----
  +--> ?PR?LCD9648_INIT_PROC?MAIN
  +--> ?PR?TIMER0_INIT?MAIN
  +--> ?PR?LCD_INIT_TEST?MAIN
  +--> ?PR?KEY_PROC?MAIN
  +--> ?PR?LCD_PROC?MAIN

?PR?LCD9648_INIT_PROC?MAIN                  -----    -----
  +--> ?PR?LCD9648_INIT?LCD9648
  +--> ?PR?LCD9648_CLEAR?LCD9648
  +--> ?CO?MAIN
  +--> ?PR?_LCD9648_WRITE16CNCHAR?LCD9648
  +--> ?PR?_LCD9648_WRITE16ENCHAR?LCD9648

?PR?LCD9648_INIT?LCD9648                    -----    -----
  +--> ?PR?_WRITECOMM?LCD9648

?PR?_WRITECOMM?LCD9648                      -----    -----
  +--> ?PR?_SENDDATASPI?LCD9648

?PR?LCD9648_CLEAR?LCD9648                   -----    -----
  +--> ?PR?_WRITECOMM?LCD9648
  +--> ?PR?_WRITEDATA?LCD9648

?PR?_WRITEDATA?LCD9648                      -----    -----
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  23:06:27  PAGE 3


  +--> ?PR?_SENDDATASPI?LCD9648

?PR?_LCD9648_WRITE16CNCHAR?LCD9648          0021H    0004H
  +--> ?PR?_WRITECOMM?LCD9648
  +--> ?CO?LCD9648
  +--> ?PR?_WRITEDATA?LCD9648

?PR?_LCD9648_WRITE16ENCHAR?LCD9648          0028H    0004H
  +--> ?PR?_WRITECOMM?LCD9648
  +--> ?CO?LCD9648
  +--> ?PR?_WRITEDATA?LCD9648

?PR?LCD_INIT_TEST?MAIN                      -----    -----
  +--> ?PR?LCD9648_INIT_PROC?MAIN

?PR?KEY_PROC?MAIN                           -----    -----
  +--> ?PR?KEY_READ?KEY

?PR?LCD_PROC?MAIN                           0021H    0007H
  +--> ?PR?LCD9648_CLEAR?LCD9648
  +--> ?CO?MAIN
  +--> ?PR?_LCD9648_WRITE16ENCHAR?LCD9648
  +--> ?PR?_FLOAT_TO_STR?MAIN

?PR?_FLOAT_TO_STR?MAIN                      0028H    0007H



SYMBOL TABLE OF MODULE:  .\Objects\Project (MAIN)

  VALUE           TYPE          NAME
  ----------------------------------

  -------         MODULE        MAIN
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0080H         PUBLIC        P0
  D:0090H         PUBLIC        P1
  D:00A0H         PUBLIC        P2
  D:00B0H         PUBLIC        P3
  C:149EH         PUBLIC        Timer0_ISR
  B:00A8H.7       PUBLIC        EA
  D:00A8H         PUBLIC        IE
  D:0008H         PUBLIC        Temperature
  D:00B8H         PUBLIC        IP
  C:0D0EH         PUBLIC        LCD9648_Init_Proc
  B:0020H.0       PUBLIC        LCD_Init_End_Flag
  C:0B0AH         PUBLIC        Key_Proc
  D:0009H         PUBLIC        Key_Down
  C:15E2H         PUBLIC        main
  C:15BDH         PUBLIC        Timer0_Init
  D:000AH         PUBLIC        Key_Old
  D:000BH         PUBLIC        Key_Slow_Down
  D:000CH         PUBLIC        LCD_Disp_Mode
  D:000DH         PUBLIC        Key_Val
  D:000EH         PUBLIC        LCD_Init_Mode
  D:0098H         PUBLIC        SCON
  D:0089H         PUBLIC        TMOD
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  23:06:27  PAGE 4


  D:0088H         PUBLIC        TCON
  C:1080H         PUBLIC        _Float_To_Str
  D:000FH         PUBLIC        Timer0_count
  B:00A8H.1       PUBLIC        ET0
  C:0E7AH         PUBLIC        LCD_Proc
  D:008CH         PUBLIC        TH0
  C:14FEH         PUBLIC        LCD_Init_Test
  D:0011H         PUBLIC        Key_Up
  D:008AH         PUBLIC        TL0
  B:0088H.4       PUBLIC        TR0
  D:0012H         PUBLIC        Time_Flag_Count
  D:0013H         PUBLIC        State_Index
  D:00C8H         PUBLIC        T2CON
  D:0014H         PUBLIC        Time_Flag
  D:0015H         PUBLIC        State_Disp
  D:00D0H         PUBLIC        PSW
  -------         PROC          L?0075
  -------         ENDPROC       L?0075
  -------         PROC          _FLOAT_TO_STR
  D:0028H         SYMBOL        num
  D:002AH         SYMBOL        str
  D:002DH         SYMBOL        unit
  D:002EH         SYMBOL        decimal_flag
  C:1080H         LINE#         26
  C:108AH         LINE#         27
  C:108AH         LINE#         28
  C:108EH         LINE#         29
  C:108EH         LINE#         30
  C:109BH         LINE#         31
  C:10BCH         LINE#         32
  C:10C4H         LINE#         33
  C:10DEH         LINE#         34
  C:10E6H         LINE#         35
  C:10E9H         LINE#         36
  C:10EBH         LINE#         38
  C:10EBH         LINE#         39
  C:1102H         LINE#         40
  C:111CH         LINE#         41
  C:1124H         LINE#         42
  C:112BH         LINE#         43
  C:112BH         LINE#         44
  -------         ENDPROC       _FLOAT_TO_STR
  -------         PROC          KEY_PROC
  C:0B0AH         LINE#         47
  C:0B0AH         LINE#         48
  C:0B0AH         LINE#         49
  C:0B11H         LINE#         50
  C:0B14H         LINE#         52
  C:0B19H         LINE#         53
  C:0B22H         LINE#         54
  C:0B28H         LINE#         55
  C:0B2BH         LINE#         57
  C:0B47H         LINE#         58
  C:0B47H         LINE#         59
  C:0B47H         LINE#         60
  C:0B50H         LINE#         61
  C:0B50H         LINE#         62
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  23:06:27  PAGE 5


  C:0B54H         LINE#         63
  C:0B54H         LINE#         64
  C:0B62H         LINE#         65
  C:0B75H         LINE#         66
  C:0B81H         LINE#         67
  C:0B81H         LINE#         69
  C:0B87H         LINE#         70
  C:0B87H         LINE#         71
  C:0B95H         LINE#         72
  C:0BA8H         LINE#         73
  C:0BB4H         LINE#         74
  C:0BB4H         LINE#         76
  C:0BBAH         LINE#         77
  C:0BBAH         LINE#         78
  C:0BC8H         LINE#         79
  C:0BDBH         LINE#         80
  C:0BE7H         LINE#         81
  C:0BE7H         LINE#         83
  C:0BF0H         LINE#         84
  C:0BF0H         LINE#         85
  C:0C0BH         LINE#         86
  C:0C17H         LINE#         87
  C:0C17H         LINE#         88
  C:0C17H         LINE#         89
  C:0C18H         LINE#         91
  C:0C18H         LINE#         92
  C:0C21H         LINE#         93
  C:0C21H         LINE#         94
  C:0C25H         LINE#         95
  C:0C25H         LINE#         96
  C:0C38H         LINE#         97
  C:0C48H         LINE#         99
  C:0C54H         LINE#         100
  C:0C54H         LINE#         102
  C:0C5AH         LINE#         103
  C:0C5AH         LINE#         104
  C:0C6DH         LINE#         105
  C:0C7DH         LINE#         107
  C:0C89H         LINE#         108
  C:0C89H         LINE#         110
  C:0C8FH         LINE#         111
  C:0C8FH         LINE#         112
  C:0CA2H         LINE#         113
  C:0CB2H         LINE#         115
  C:0CBEH         LINE#         116
  C:0CBEH         LINE#         118
  C:0CC4H         LINE#         119
  C:0CC4H         LINE#         120
  C:0CD7H         LINE#         121
  C:0CE6H         LINE#         123
  C:0CF2H         LINE#         124
  C:0CF2H         LINE#         125
  C:0CF2H         LINE#         126
  C:0CF3H         LINE#         128
  C:0CF3H         LINE#         129
  C:0CFDH         LINE#         130
  C:0CFEH         LINE#         132
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  23:06:27  PAGE 6


  C:0CFEH         LINE#         133
  C:0D03H         LINE#         134
  C:0D03H         LINE#         135
  C:0D0AH         LINE#         136
  C:0D0DH         LINE#         137
  C:0D0DH         LINE#         138
  C:0D0DH         LINE#         139
  C:0D0DH         LINE#         140
  -------         ENDPROC       KEY_PROC
  -------         PROC          TIMER0_INIT
  C:15BDH         LINE#         150
  C:15BDH         LINE#         151
  C:15BDH         LINE#         152
  C:15C0H         LINE#         153
  C:15C3H         LINE#         154
  C:15C6H         LINE#         155
  C:15C9H         LINE#         156
  C:15CBH         LINE#         157
  C:15CDH         LINE#         158
  C:15CFH         LINE#         159
  -------         ENDPROC       TIMER0_INIT
  -------         PROC          TIMER0_ISR
  C:149EH         LINE#         165
  C:14A2H         LINE#         167
  C:14A5H         LINE#         168
  C:14A8H         LINE#         170
  C:14B0H         LINE#         172
  C:14B9H         LINE#         173
  C:14B9H         LINE#         174
  C:14BCH         LINE#         175
  C:14C2H         LINE#         177
  C:14C2H         LINE#         179
  C:14CCH         LINE#         180
  -------         ENDPROC       TIMER0_ISR
  -------         PROC          LCD9648_INIT_PROC
  C:0D0EH         LINE#         181
  C:0D0EH         LINE#         182
  C:0D0EH         LINE#         183
  C:0D24H         LINE#         184
  C:0D24H         LINE#         185
  C:0D24H         LINE#         186
  C:0D27H         LINE#         187
  C:0D2AH         LINE#         189
  C:0D36H         LINE#         190
  C:0D43H         LINE#         191
  C:0D50H         LINE#         193
  C:0D5DH         LINE#         195
  C:0D6AH         LINE#         196
  C:0D77H         LINE#         197
  C:0D84H         LINE#         198
  C:0D91H         LINE#         199
  C:0D9EH         LINE#         200
  C:0D9EH         LINE#         202
  C:0D9EH         LINE#         203
  C:0DA1H         LINE#         204
  C:0DA4H         LINE#         206
  C:0DB0H         LINE#         207
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  23:06:27  PAGE 7


  C:0DBDH         LINE#         208
  C:0DCAH         LINE#         209
  C:0DD7H         LINE#         210
  C:0DE4H         LINE#         211
  C:0DF1H         LINE#         213
  C:0DFEH         LINE#         214
  C:0E0BH         LINE#         215
  C:0E18H         LINE#         216
  C:0E25H         LINE#         217
  C:0E32H         LINE#         218
  C:0E3FH         LINE#         220
  C:0E4CH         LINE#         221
  C:0E59H         LINE#         222
  C:0E66H         LINE#         223
  C:0E73H         LINE#         224
  C:0E73H         LINE#         226
  C:0E73H         LINE#         227
  C:0E76H         LINE#         228
  C:0E79H         LINE#         236
  C:0E79H         LINE#         238
  C:0E79H         LINE#         240
  C:0E79H         LINE#         241
  C:0E79H         LINE#         242
  -------         ENDPROC       LCD9648_INIT_PROC
  -------         PROC          LCD_INIT_TEST
  C:14FEH         LINE#         244
  C:14FEH         LINE#         245
  C:14FEH         LINE#         246
  C:1502H         LINE#         247
  C:1502H         LINE#         248
  C:1505H         LINE#         249
  C:1507H         LINE#         250
  C:1509H         LINE#         251
  C:1510H         LINE#         252
  C:1510H         LINE#         253
  C:1513H         LINE#         254
  C:1513H         LINE#         255
  C:1518H         LINE#         256
  C:151DH         LINE#         257
  C:151DH         LINE#         259
  -------         ENDPROC       LCD_INIT_TEST
  -------         PROC          LCD_PROC
  -------         DO            
  D:0021H         SYMBOL        temp_str
  D:0027H         SYMBOL        i
  -------         ENDDO         
  C:0E7AH         LINE#         267
  C:0E7AH         LINE#         268
  C:0E7AH         LINE#         272
  C:0E80H         LINE#         273
  C:0E80H         LINE#         274
  C:0E84H         LINE#         275
  C:0E84H         LINE#         277
  C:0E87H         LINE#         280
  C:0E93H         LINE#         281
  C:0EA0H         LINE#         282
  C:0EADH         LINE#         286
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  23:06:27  PAGE 8


  C:0EB0H         LINE#         287
  C:0EBDH         LINE#         290
  C:0ED0H         LINE#         291
  C:0EDDH         LINE#         294
  C:0EF0H         LINE#         295
  C:0EFAH         LINE#         296
  C:0EFDH         LINE#         297
  C:0F06H         LINE#         298
  C:0F06H         LINE#         300
  C:0F09H         LINE#         303
  C:0F0CH         LINE#         304
  C:0F0CH         LINE#         305
  C:0F11H         LINE#         306
  C:0F11H         LINE#         307
  C:0F1AH         LINE#         308
  C:0F1CH         LINE#         310
  C:0F1CH         LINE#         311
  C:0F2CH         LINE#         312
  C:0F2CH         LINE#         313
  C:0F35H         LINE#         316
  C:0F42H         LINE#         317
  C:0F45H         LINE#         318
  C:0F52H         LINE#         320
  C:0F5FH         LINE#         321
  C:0F72H         LINE#         322
  C:0F7FH         LINE#         324
  C:0F8CH         LINE#         325
  C:0F9FH         LINE#         326
  C:0FACH         LINE#         327
  C:0FACH         LINE#         328
  C:0FACH         LINE#         329
  -------         ENDPROC       LCD_PROC
  -------         PROC          MAIN
  C:15E2H         LINE#         332
  C:15E2H         LINE#         333
  C:15E2H         LINE#         334
  C:15E5H         LINE#         335
  C:15E8H         LINE#         337
  C:15E8H         LINE#         338
  C:15E8H         LINE#         339
  C:15EBH         LINE#         341
  C:15EEH         LINE#         342
  C:15F1H         LINE#         343
  -------         ENDPROC       MAIN
  -------         ENDMOD        MAIN

  -------         MODULE        DS18B20
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  C:1602H         PUBLIC        ds18b20_reset
  D:00A8H         PUBLIC        IE
  C:0003H         PUBLIC        ds18b20_start
  D:00B8H         PUBLIC        IP
  C:153EH         PUBLIC        ds18b20_read_byte
  C:1610H         PUBLIC        ds18b20_init
  C:1574H         PUBLIC        ds18b20_read_bit
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  23:06:27  PAGE 9


  C:1472H         PUBLIC        _ds18b20_write_byte
  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
  C:12F2H         PUBLIC        ds18b20_read_temperture
  C:15F3H         PUBLIC        _delay_10us
  B:00B0H.7       PUBLIC        DS18B20_PORT
  D:00C8H         PUBLIC        T2CON
  C:142FH         PUBLIC        ds18b20_check
  D:00D0H         PUBLIC        PSW
  -------         PROC          _DELAY_10US
  D:0007H         SYMBOL        xms
  -------         DO            
  D:0006H         SYMBOL        i
  -------         ENDDO         
  C:15F3H         LINE#         4
  C:15F3H         LINE#         5
  C:15F3H         LINE#         7
  C:15F9H         LINE#         8
  C:15F9H         LINE#         9
  C:15FBH         LINE#         10
  C:1601H         LINE#         12
  -------         ENDPROC       _DELAY_10US
  -------         PROC          DS18B20_RESET
  C:1602H         LINE#         20
  C:1602H         LINE#         21
  C:1602H         LINE#         22
  C:1604H         LINE#         23
  C:1609H         LINE#         24
  C:160BH         LINE#         25
  -------         ENDPROC       DS18B20_RESET
  -------         PROC          DS18B20_CHECK
  -------         DO            
  D:0004H         SYMBOL        time_temp
  -------         ENDDO         
  C:142FH         LINE#         34
  C:142FH         LINE#         35
  C:142FH         LINE#         36
  C:1431H         LINE#         38
  C:143AH         LINE#         39
  C:143AH         LINE#         40
  C:143BH         LINE#         41
  C:1440H         LINE#         42
  C:1442H         LINE#         43
  C:144BH         LINE#         44
  C:144DH         LINE#         45
  C:1456H         LINE#         46
  C:1456H         LINE#         47
  C:1457H         LINE#         48
  C:145CH         LINE#         49
  C:145EH         LINE#         50
  C:1467H         LINE#         51
  C:1469H         LINE#         52
  -------         ENDPROC       DS18B20_CHECK
  -------         PROC          DS18B20_READ_BIT
  -------         DO            
  D:0004H         SYMBOL        dat
  -------         ENDDO         
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  23:06:27  PAGE 10


  C:1574H         LINE#         60
  C:1574H         LINE#         61
  C:1574H         LINE#         62
  C:1576H         LINE#         64
  C:1578H         LINE#         65
  C:157AH         LINE#         66
  C:157CH         LINE#         67
  C:157EH         LINE#         68
  C:1584H         LINE#         69
  C:1586H         LINE#         70
  C:158BH         LINE#         71
  C:158DH         LINE#         72
  -------         ENDPROC       DS18B20_READ_BIT
  -------         PROC          DS18B20_READ_BYTE
  -------         DO            
  D:0003H         SYMBOL        i
  D:0002H         SYMBOL        dat
  D:0007H         SYMBOL        temp
  -------         ENDDO         
  C:153EH         LINE#         80
  C:153EH         LINE#         81
  C:153EH         LINE#         82
  C:1540H         LINE#         83
  C:1541H         LINE#         84
  C:1542H         LINE#         86
  C:1542H         LINE#         87
  C:1542H         LINE#         88
  C:1545H         LINE#         89
  C:1552H         LINE#         90
  C:1556H         LINE#         91
  C:1558H         LINE#         92
  -------         ENDPROC       DS18B20_READ_BYTE
  -------         PROC          L?0035
  -------         ENDPROC       L?0035
  -------         PROC          _DS18B20_WRITE_BYTE
  D:0004H         SYMBOL        dat
  -------         DO            
  D:0003H         SYMBOL        i
  D:0002H         SYMBOL        temp
  -------         ENDDO         
  C:1472H         LINE#         100
  C:1474H         LINE#         101
  C:1474H         LINE#         102
  C:1476H         LINE#         103
  C:1476H         LINE#         105
  C:1476H         LINE#         106
  C:1476H         LINE#         107
  C:147AH         LINE#         108
  C:147EH         LINE#         109
  C:1481H         LINE#         110
  C:1481H         LINE#         111
  C:1483H         LINE#         112
  C:1485H         LINE#         113
  C:1487H         LINE#         114
  C:148CH         LINE#         115
  C:148EH         LINE#         117
  C:148EH         LINE#         118
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  23:06:27  PAGE 11


  C:1490H         LINE#         119
  C:1495H         LINE#         120
  C:1497H         LINE#         121
  C:1499H         LINE#         122
  C:1499H         LINE#         123
  C:149DH         LINE#         124
  -------         ENDPROC       _DS18B20_WRITE_BYTE
  -------         PROC          DS18B20_START
  C:0003H         LINE#         132
  C:0003H         LINE#         133
  C:0003H         LINE#         134
  C:0003H         LINE#         135
  C:0003H         LINE#         136
  C:0006H         LINE#         137
  -------         ENDPROC       DS18B20_START
  -------         PROC          DS18B20_INIT
  C:1610H         LINE#         146
  C:1610H         LINE#         147
  C:1610H         LINE#         148
  C:1613H         LINE#         149
  C:1616H         LINE#         150
  -------         ENDPROC       DS18B20_INIT
  -------         PROC          DS18B20_READ_TEMPERTURE
  -------         DO            
  D:002FH         SYMBOL        temp
  D:0007H         SYMBOL        dath
  D:0001H         SYMBOL        datl
  D:0033H         SYMBOL        value
  -------         ENDDO         
  C:12F2H         LINE#         158
  C:12F2H         LINE#         159
  C:12F2H         LINE#         161
  C:12F4H         LINE#         162
  C:12F5H         LINE#         163
  C:12F9H         LINE#         165
  C:12FCH         LINE#         166
  C:12FCH         LINE#         167
  C:12FCH         LINE#         168
  C:12FFH         LINE#         169
  C:1304H         LINE#         171
  C:1309H         LINE#         172
  C:130CH         LINE#         173
  C:1318H         LINE#         175
  C:131FH         LINE#         176
  C:131FH         LINE#         177
  C:1330H         LINE#         178
  C:133EH         LINE#         179
  C:1340H         LINE#         181
  C:1340H         LINE#         182
  C:135AH         LINE#         183
  C:135AH         LINE#         184
  C:1362H         LINE#         185
  -------         ENDPROC       DS18B20_READ_TEMPERTURE
  -------         ENDMOD        DS18B20

  -------         MODULE        LCD9648
  C:0000H         SYMBOL        _ICE_DUMMY_
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  23:06:27  PAGE 12


  D:0080H         PUBLIC        P0
  D:0090H         PUBLIC        P1
  D:00A0H         PUBLIC        P2
  D:00B0H         PUBLIC        P3
  C:156AH         PUBLIC        _WriteData
  C:11D8H         PUBLIC        _LCD9648_Write16EnCHAR1
  D:00A8H         PUBLIC        IE
  D:00B8H         PUBLIC        IP
  C:15D8H         PUBLIC        _WriteComm
  B:00A0H.7       PUBLIC        RS
  C:1363H         PUBLIC        LCD9648_Init
  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
  B:0080H.0       PUBLIC        CS0
  C:000EH         PUBLIC        CN16CHARfont
  C:0250H         PUBLIC        EN16CHARfont
  B:00A0H.5       PUBLIC        SDA
  C:0FADH         PUBLIC        _LCD9648_Write16CnCHAR
  C:112CH         PUBLIC        _LCD9648_Write16EnCHAR
  B:00A0H.6       PUBLIC        SCL
  D:00C8H         PUBLIC        T2CON
  C:158EH         PUBLIC        _SendDataSPI
  B:0080H.1       PUBLIC        RST
  C:14D1H         PUBLIC        LCD9648_Clear
  D:00D0H         PUBLIC        PSW
  -------         PROC          _SENDDATASPI
  D:0007H         SYMBOL        dat
  -------         DO            
  D:0006H         SYMBOL        i
  -------         ENDDO         
  C:158EH         LINE#         4
  C:158EH         LINE#         5
  C:158EH         LINE#         8
  C:1590H         LINE#         9
  C:1590H         LINE#         10
  C:1598H         LINE#         11
  C:159AH         LINE#         13
  C:159EH         LINE#         15
  C:15A0H         LINE#         16
  C:15A2H         LINE#         17
  C:15A6H         LINE#         18
  -------         ENDPROC       _SENDDATASPI
  C:15D2H         SYMBOL        L?0067
  C:15D4H         SYMBOL        L?0068
  -------         PROC          L?0066
  -------         ENDPROC       L?0066
  C:15D2H         SYMBOL        L?0067
  C:15D4H         SYMBOL        L?0068
  -------         PROC          _WRITECOMM
  D:0006H         SYMBOL        i
  C:15D8H         LINE#         20
  C:15D8H         LINE#         21
  C:15D8H         LINE#         23
  C:15DAH         LINE#         24
  C:15DCH         LINE#         26
  C:15DFH         LINE#         28
  C:15E1H         LINE#         30
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  23:06:27  PAGE 13


  -------         ENDPROC       _WRITECOMM
  C:155EH         SYMBOL        L?0070
  -------         PROC          L?0069
  -------         ENDPROC       L?0069
  C:155EH         SYMBOL        L?0070
  -------         PROC          _WRITEDATA
  D:0006H         SYMBOL        i
  C:156AH         LINE#         31
  C:156AH         LINE#         32
  C:156AH         LINE#         33
  C:156CH         LINE#         34
  C:156EH         LINE#         36
  C:1571H         LINE#         38
  C:1573H         LINE#         39
  -------         ENDPROC       _WRITEDATA
  -------         PROC          LCD9648_INIT
  -------         DO            
  D:0006H         SYMBOL        i
  -------         ENDDO         
  C:1363H         LINE#         41
  C:1363H         LINE#         42
  C:1363H         LINE#         46
  C:1365H         LINE#         47
  C:1373H         LINE#         49
  C:1375H         LINE#         50
  C:1383H         LINE#         52
  C:1385H         LINE#         53
  C:1393H         LINE#         55
  C:139AH         LINE#         56
  C:13A1H         LINE#         57
  C:13A8H         LINE#         58
  C:13AFH         LINE#         59
  C:13B6H         LINE#         60
  C:13BDH         LINE#         61
  C:13C4H         LINE#         62
  -------         ENDPROC       LCD9648_INIT
  -------         PROC          LCD9648_CLEAR
  -------         DO            
  D:0005H         SYMBOL        i
  D:0004H         SYMBOL        j
  -------         ENDDO         
  C:14D1H         LINE#         67
  C:14D1H         LINE#         68
  C:14D1H         LINE#         71
  C:14D3H         LINE#         72
  C:14D3H         LINE#         73
  C:14DAH         LINE#         74
  C:14E0H         LINE#         75
  C:14E7H         LINE#         76
  C:14EDH         LINE#         78
  C:14EFH         LINE#         79
  C:14EFH         LINE#         80
  C:14F5H         LINE#         81
  C:14F9H         LINE#         82
  C:14FDH         LINE#         83
  -------         ENDPROC       LCD9648_CLEAR
  -------         PROC          _LCD9648_WRITE16CNCHAR
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  23:06:27  PAGE 14


  D:0021H         SYMBOL        x
  D:0022H         SYMBOL        y
  D:0001H         SYMBOL        cn
  -------         DO            
  D:0004H         SYMBOL        j
  D:0023H         SYMBOL        x1
  D:0024H         SYMBOL        x2
  D:0005H         SYMBOL        wordNum
  -------         ENDDO         
  C:0FADH         LINE#         86
  C:0FB1H         LINE#         87
  C:0FB1H         LINE#         91
  C:0FB8H         LINE#         92
  C:0FB8H         LINE#         93
  C:0FBBH         LINE#         94
  C:0FBBH         LINE#         97
  C:0FC2H         LINE#         98
  C:0FC2H         LINE#         99
  C:0FC5H         LINE#         100
  C:0FC5H         LINE#         101
  C:0FCBH         LINE#         103
  C:0FD1H         LINE#         104
  C:0FD9H         LINE#         105
  C:0FD9H         LINE#         108
  C:0FE0H         LINE#         110
  C:0FE7H         LINE#         111
  C:0FEDH         LINE#         113
  C:0FF0H         LINE#         114
  C:0FF7H         LINE#         115
  C:0FF9H         LINE#         116
  C:0FF9H         LINE#         118
  C:102CH         LINE#         120
  C:102CH         LINE#         121
  C:102DH         LINE#         122
  C:102DH         LINE#         123
  C:1032H         LINE#         124
  C:1032H         LINE#         126
  C:1039H         LINE#         129
  C:103CH         LINE#         130
  C:1043H         LINE#         131
  C:1043H         LINE#         132
  C:1053H         LINE#         133
  C:1057H         LINE#         134
  C:105DH         LINE#         135
  C:105DH         LINE#         136
  C:1063H         LINE#         137
  C:106AH         LINE#         139
  C:106DH         LINE#         140
  C:106FH         LINE#         141
  -------         ENDPROC       _LCD9648_WRITE16CNCHAR
  -------         PROC          _LCD9648_WRITE16ENCHAR
  D:0028H         SYMBOL        x
  D:0029H         SYMBOL        y
  D:0001H         SYMBOL        en
  -------         DO            
  D:0004H         SYMBOL        j
  D:002AH         SYMBOL        x1
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  23:06:27  PAGE 15


  D:002BH         SYMBOL        x2
  D:0005H         SYMBOL        wordNum
  -------         ENDDO         
  C:112CH         LINE#         143
  C:1130H         LINE#         144
  C:1130H         LINE#         148
  C:1137H         LINE#         149
  C:1137H         LINE#         150
  C:113AH         LINE#         151
  C:113AH         LINE#         154
  C:1141H         LINE#         155
  C:1141H         LINE#         156
  C:1144H         LINE#         157
  C:1144H         LINE#         158
  C:114AH         LINE#         160
  C:1150H         LINE#         161
  C:1158H         LINE#         162
  C:1158H         LINE#         165
  C:115FH         LINE#         167
  C:1166H         LINE#         168
  C:116CH         LINE#         170
  C:1171H         LINE#         171
  C:1178H         LINE#         172
  C:117AH         LINE#         173
  C:117AH         LINE#         175
  C:1192H         LINE#         176
  C:1192H         LINE#         177
  C:1193H         LINE#         178
  C:1193H         LINE#         179
  C:1198H         LINE#         180
  C:1198H         LINE#         182
  C:119FH         LINE#         185
  C:11A4H         LINE#         186
  C:11ABH         LINE#         187
  C:11ABH         LINE#         188
  C:11BBH         LINE#         189
  C:11BFH         LINE#         190
  C:11C5H         LINE#         191
  C:11C5H         LINE#         192
  C:11CBH         LINE#         193
  C:11D2H         LINE#         195
  C:11D5H         LINE#         196
  C:11D7H         LINE#         197
  -------         ENDPROC       _LCD9648_WRITE16ENCHAR
  -------         PROC          _LCD9648_WRITE16ENCHAR1
  D:0001H         SYMBOL        x
  D:0005H         SYMBOL        y
  D:0003H         SYMBOL        en
  -------         DO            
  D:0002H         SYMBOL        j
  D:001DH         SYMBOL        x1
  D:001EH         SYMBOL        x2
  D:0004H         SYMBOL        wordNum
  -------         ENDDO         
  C:11D8H         LINE#         198
  C:11DAH         LINE#         199
  C:11DAH         LINE#         203
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  23:06:27  PAGE 16


  C:11E0H         LINE#         204
  C:11E0H         LINE#         205
  C:11E3H         LINE#         206
  C:11E3H         LINE#         209
  C:11E9H         LINE#         210
  C:11E9H         LINE#         211
  C:11ECH         LINE#         212
  C:11ECH         LINE#         213
  C:11F0H         LINE#         215
  C:11F6H         LINE#         217
  C:11F6H         LINE#         220
  C:11FDH         LINE#         222
  C:1203H         LINE#         223
  C:1208H         LINE#         225
  C:120DH         LINE#         226
  C:1214H         LINE#         227
  C:1216H         LINE#         228
  C:1216H         LINE#         230
  C:122AH         LINE#         231
  C:122AH         LINE#         232
  C:122BH         LINE#         233
  C:122BH         LINE#         234
  C:1230H         LINE#         235
  C:1230H         LINE#         237
  C:1236H         LINE#         240
  C:123BH         LINE#         241
  C:1242H         LINE#         242
  C:1242H         LINE#         243
  C:1257H         LINE#         244
  C:125BH         LINE#         245
  C:125FH         LINE#         246
  C:125FH         LINE#         247
  C:1263H         LINE#         248
  C:1263H         LINE#         250
  C:1263H         LINE#         251
  C:1265H         LINE#         252
  -------         ENDPROC       _LCD9648_WRITE16ENCHAR1
  -------         ENDMOD        LCD9648

  -------         MODULE        KEY
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  C:15A7H         PUBLIC        Key_Read
  D:00A8H         PUBLIC        IE
  D:00B8H         PUBLIC        IP
  B:0090H.0       PUBLIC        k1
  B:0090H.1       PUBLIC        k2
  B:0090H.2       PUBLIC        k3
  B:0090H.3       PUBLIC        k4
  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
  D:00C8H         PUBLIC        T2CON
  D:00D0H         PUBLIC        PSW
  -------         PROC          KEY_READ
  -------         DO            
  D:0007H         SYMBOL        temp
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  23:06:27  PAGE 17


  -------         ENDDO         
  C:15A7H         LINE#         4
  C:15A7H         LINE#         5
  C:15A7H         LINE#         6
  C:15A9H         LINE#         8
  C:15ADH         LINE#         9
  C:15B2H         LINE#         10
  C:15B7H         LINE#         11
  C:15BCH         LINE#         12
  C:15BCH         LINE#         13
  -------         ENDPROC       KEY_READ
  -------         ENDMOD        KEY

  -------         MODULE        ?C?FPMUL
  C:089FH         PUBLIC        ?C?FPMUL
  -------         ENDMOD        ?C?FPMUL

  -------         MODULE        ?C?FCAST
  C:09AFH         PUBLIC        ?C?FCASTC
  C:09AAH         PUBLIC        ?C?FCASTI
  C:09A5H         PUBLIC        ?C?FCASTL
  -------         ENDMOD        ?C?FCAST

  -------         MODULE        ?C?CLDPTR
  C:0A2DH         PUBLIC        ?C?CLDPTR
  -------         ENDMOD        ?C?CLDPTR

  -------         MODULE        ?C?CLDOPTR
  C:0A46H         PUBLIC        ?C?CLDOPTR
  -------         ENDMOD        ?C?CLDOPTR

  -------         MODULE        ?C?CSTPTR
  C:0A73H         PUBLIC        ?C?CSTPTR
  -------         ENDMOD        ?C?CSTPTR

  -------         MODULE        ?C?CSTOPTR
  C:0A85H         PUBLIC        ?C?CSTOPTR
  -------         ENDMOD        ?C?CSTOPTR

  -------         MODULE        ?C?UIDIV
  C:0AA7H         PUBLIC        ?C?UIDIV
  -------         ENDMOD        ?C?UIDIV

  -------         MODULE        ?C?LNEG
  C:0AFCH         PUBLIC        ?C?LNEG
  -------         ENDMOD        ?C?LNEG

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?DS18B20_INIT?DS18B20

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?DS18B20_READ_TEMPERTURE?DS18B20

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?_LCD9648_WRITE16ENCHAR1?LCD9648

Program Size: data=52.1 xdata=0 code=5655
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  23:06:27  PAGE 18


LINK/LOCATE RUN COMPLETE.  3 WARNING(S),  0 ERROR(S)
